var logger          = require('pomelo-logger').getLogger('user-log', __filename);
var UserStore       = require('../../../persistence/users');
var CODE            = require('../../../consts/code');
const CONSTS        = require('../../../consts/consts');
const FRIEND_TYPE   = require('../../../consts/friendType');
const LogTypes      = require('../../../consts/logTypes');

module.exports = function(app){
    return new Handler(app);
};
var Handler = function(app){
    this.app = app;
};
var handler = Handler.prototype;


// get user info
handler.getUserInfo = function (msg, session, next) {
    logger.info("[userHandler.getUserInfo] msg: ", msg);

    if (!session.uid) {
        return next(null, {
            code  : 500,
            error : 'invalid-session'
        });
    }

    if (!msg.uid) {
        return next(null, {
            code  : 500,
            error : 'invalid-input'
        });
    }


    var uid = msg.uid ?? 0; // đây là id trong player table
    // var usersService = this.app.get('usersService');
    this.app.get('usersService').getUserInfoHandler(uid, function (e, res) {
        logger.info("e => ", e, " => res: ", res);
        if (e) {
            next(null, {
                code    : e,
                route   : msg.route,
                msg     : 'Không tìm thấy thông tin người dùng'
            });
            return;
        }

        next(null, {
            code    : 200,
            route   : msg.route,
            msg     : res
        });

    });
};


/**
 * Xem thông tin của 1 người chơi (có thể là bạn bè)
 * @param {Object} msg 
 * @param {Object} session 
 * @param {Function} next 
 * @returns 
 */
handler.viewPlayerInfo = function (msg, session, next) {
    logger.info("[userHandler.getPlayerDetail] msg: ", msg);

    if (!session.uid) {
        return next(null, {
            code  : 500,
            error : 'invalid-session'
        });
    }

    if (!msg.uid) {
        return next(null, {
            code  : 500,
            error : 'invalid-input'
        });
    }

    var uid = msg.uid ?? 0; // đây là id trong player table
    this.app.get('usersService').getPlayerInfo(uid, function (e, res) {
        logger.info("e => ", e, " => res: ", res);
        if (e) {
            next(null, {
                code    : e,
                route   : msg.route,
                msg     : 'Không tìm thấy thông tin người dùng'
            });
            return;
        }

        next(null, {
            code    : 200,
            route   : msg.route,
            data    : res
        });

    });
};

/**
 * @description hàm lấy thông tin 1 user
 * @param msg
 * @param session
 * @param next
 */
handler.getUser = function (msg, session, next) {
    // userDao.getUserById(uid, function(e, user) {
    this.app.rpc.authen.authenRemote.getUserInfo(session, msg.userId, (e, code, user) => {
        logger.info("getUser >> user ", user, ' -> code: ', code, ' -> err: ', e);

        // if(!user){
        if(e) {
            return next(null, {
                code  : CODE.NOT_FOUND,
                error : e,
                data: "User " + msg.userId + " không tồn tại"
            });
        }

        next(null, {
            code    : 200,
            route   : msg.route,
            matches : user?.data ?? null
        });

    });
};

const dataList = [
    {
        uid: 1527552960590655,
        f_uid: "10154542941987514",
        displayName: "Harry",
        isOnline: true,
        last_login: 1503976465,
        type: 2
    },
    {
        uid: 1527552960590655,
        f_uid: "10155414808481287",
        displayName: "Le Son",
        isOnline: false,
        last_login: 1503976465,
        type: 2
    },
    {
        uid: 1527552960590655,
        f_uid: "10156032012224838",
        displayName: "Tiến Phạm",
        isOnline: true,
        last_login: 1503976465,
        type: 2
    },
    {
        uid: 1527552960590655,
        f_uid: "10202893083924123",
        displayName: "Nguyễn Trường Sinh",
        isOnline: true,
        last_login: 1503976465,
        type: 2
    },
    {
        uid: 1527552960590655,
        f_uid: "10213002161797098",
        displayName: "Jason Nguyen",
        isOnline: false,
        last_login: 1503976465,
        type: 2
    },
    {
        uid: 1527552960590655,
        f_uid: "1636456226383697",
        displayName: "Đỗ Tùng",
        isOnline: true,
        last_login: 1503976465,
        type: 2
    },
    {
        uid: 1527552960590655,
        f_uid: "1722557764711940",
        displayName: "Minh Tung Nguyen",
        isOnline: false,
        last_login: 1503976465,
        type: 2
    },
    {
        uid: 1527552960590655,
        f_uid: "1758539977493284",
        displayName: "Toàn Nguyễn",
        isOnline: true,
        last_login: 1503976465,
        type: 2
    }
];

/**
 * Lấy danh sách người chơi ngẫu nhiên (phục vụ cho màn hình kết bạn) (done)
 * @param {Object} msg message from client
 * @param {Object} session session of user
 * @param  {Function} next next step callback
 * @return {void}
 */
handler.getRandomUsers = async function (msg, session, next) {
    if (!session.uid) {
        return next(null, {
            code  : 500,
            error : 'invalid-session'
        });
    }

    const uid   = session.uid; // uuid của user
    const limit = msg.limit || CONSTS.EVENT.PAGESIZE;

    const friendService  = this.app.get('friendService');
    logger.info("call getFriends with uid", uid , ' -> limit: ', limit);

    friendService.getRandomUsersHandler(uid, limit, function (e, res) {

        res = (res == null) ? [] : res;
        logger.info("getRandomUsers >> res ", res, ' -> e: ', e);

        next(null, {
            code    : 200,
            route   : msg.route,
            msg     : res
        });

    });

};

// get list friends
handler.getFriends = async function (msg, session, next) {
    if (!session.uid) {
        return next(null, {
            code  : 500,
            error : 'invalid-session'
        });
    }
    // var uid             = msg.uid; // uuid của user
    const uid           = session.uid; // uuid của user
    var page            = msg.page || 1;
    var limit           = msg.limit || CONSTS.EVENT.PAGESIZE;
    // 1 = lấy danh sách người chơi đã theo dõi (là người đã gửi request nhưng chưa đc chấp nhận)
    // 2 = lấy là danh sách bạn bè đã được chấp nhận
    // 3 = lấy danh sách đen (nhóm người chơi đã bị chặn)
    var type            = msg.type || 0;
    var friendService   = this.app.get('friendService');

    logger.info("call getFriends with page: ", page, " type : ", type, ' -> uid: ', uid, ' -> limit: ', limit);

    // Step 2: Get data of friends
    // --------------------------------------------------------------------------------------------------------
    friendService.handleGetFriends(uid, type, page, limit, function (e, res) {

        res = (res == null) ? [] : res;
        logger.info("getFriends >> res ", res, ' -> e: ', e);

        // gán dữ liệu test
        // var result = {};
        // if (page <= 5) {
        //     result = {
        //         data: dataList,
        //         total: 100
        //     };
        // } else {
        //     result = {
        //         data: [],
        //         total: 100
        //     };
        // }

        next(null, {
            code    : 200,
            route   : msg.route,
            msg     : res
        });

    });

};

/**
 * Gửi yêu cầu mời chơi (done)
 * Tham số gồm:
 * - sender_id người mời
 * - receiver_id người được mời
 * - pos vị trí ngồi
 * - tid id của bàn
 * @param msg
 * @param session
 * @param next
 */
handler.sendInvitePlay = function (msg, session, next) {
    logger.info("[userHandler.sendInvitePlay] with msg: ", msg);
    if(!session.uid) {
        return next(null, {
            code  : 500,
            error : 'invalid-session'
        });
    }

    let uid, username, fid, tid, pos, sender_id, receiver_id;
    // uid         = msg.uid;
    // sender_id     = msg.sender_id;
    sender_id   = session.uid;
    // fid         = msg.fid;
    // user_id của người nhận request
    receiver_id = msg.receiver_id;
    tid         = msg.tid;
    pos         = msg.pos;
    username    = msg.username;

    const data = {
        uid: sender_id,
        username: username,
        fid: receiver_id,
        tid: tid,
        pos: pos
    };

    logger.info("[userHandler.sendInvitePlay] table Id: tid ", tid);
    logger.info("[userHandler.sendInvitePlay] - người mời id: ", sender_id);
    logger.info("[userHandler.sendInvitePlay] - người được mời id: ", receiver_id);
    logger.info("[userHandler.sendInvitePlay] - vị trí ngồi: ", pos);
    logger.info("[userHandler.sendInvitePlay] - session.uid: ", session.uid);
    logger.info("[userHandler.sendInvitePlay] - session.frontendId: ", session.frontendId);
    logger.info("[userHandler.sendInvitePlay] - data: ", data);

    var usersService = this.app.get('usersService');
    usersService.invitePlayHandler(data, function (e, res) {

        logger.info("[userHandler.sendInvitePlay] >> invitePlayHandler >> data return => ", e, " => res: ", res);

        next(null, {
            code    : 200,
            route   : msg.route
        });

    });

};

/**
 * Tìm kiếm bạn bè theo tên hoặc uid (không phải user_id)
 * @param msg
 * @param session
 * @param next
 */
handler.searchFriends = function (msg, session, next) {
    logger.info("[chatHandler.searchFriends] with msg: ", msg);
    // let uid, keyword, data;
    let userId, keyword, data;
    // uid     = msg.uid,
    keyword         = msg.keyword;
    // let type        = msg.type || 2;
    let type        = msg.type || 0; // mặc định type = 0 thì sẽ tìm kiếm all
    const page      = msg.page || 1;
    const limit     = msg.limit || CONSTS.EVENT.PAGESIZE;

    if (!session.uid) {
        return next(null, {
            code  : 500,
            error : 'invalid-session',
            route   : msg.route
        });
    }

    if (!keyword) {
        return next(null, {
            code  : 500,
            error : 'content-is-empty',
            route   : msg.route
        });
    }

    userId = session.uid;
    logger.info("[searchFriends] with keyword: ", keyword, " => session.uid ", session.uid, " => session.id: ", session.id);

    // nếu uid hợp lệ, tìm kiếm friend theo keyword gửi lên
    // --------------------------------------------------------------------------------------------------------
    var friendService = this.app.get('friendService');
    data = {
        uid: userId,
        type: type,
        keyword: keyword,
        page: page,
        limit: limit
    };

    logger.info("[userHandler.searchFriends] with data: ", data);

    if (type > 0) {

        friendService.searchFriendsHandler(data, function (e, res) {

            logger.info("[userHandler.searchFriends] >> searchFriendsHandler >> data return => ", e, " => res: ", res);

            next(null, {
                code    : 200,
                route   : msg.route,
                msg     : res
            });

        });

    } else {

        var userService = this.app.get('usersService');

        userService.searchPlayer(data, function (e, res) {

            logger.info("[userHandler.searchFriends] >> searchPlayer >> data return => ", e, " => res: ", res);

            next(null, {
                code    : 200,
                route   : msg.route,
                msg     : res
            });

        });
    }

};


/**
 * Tip tặng phần thưỏng cho dealer
 * @param msg
 * @param session
 * @param next
 */
handler.tipDealer = function (msg, session, next) {
    logger.info("[userHandler.tipDealer] with msg: ", msg);
    var uid, tipMoney, usersService, type;
    
    if (!session.uid) {
        return next(null, {
            code  : 500,
            error : 'invalid-session'
        });
    }

    if (!tipMoney) {
        return next(null, {
            code  : 500,
            error : 'content-is-empty'
        });
    }

    // Lấy uid , số tiền tip
    uid = session.uid;
    tipMoney = msg.amount;
    type = msg.type || 'CHIP'; // CHIP, PROPS

    logger.info("[userHandler.tipDealer] Send command tipDealer of uid ", uid, " and amount ", tipMoney, ' -> type: ', type);

    usersService = this.app.get('usersService');

    usersService.tipDealerHandler(uid, tipMoney, type, function (code, res) {

        logger.info("[userHandler.tipDealer] tipDealer >> res ", res, ' -> code: ', code);

        return next(null, {
            code: code,
            route: msg.route,
            data: res
        });
    });

};


handler.kickUser = function (msg, session, next) {

    var uid = msg.uid;
    // step 1: check uid co trong cached hay khong

    var self = this;
    this.app.get('tableService').kickUser(uid, function (ret) {

        next(null, {
            code    : 200,
            route   : msg.route,
            msg     : ret
        });

    });

};


/**
 * Get users matching the criteria
 *
 * @param {Object} msg game parameters from client
 * @param {Object} session
 * @param  {Function} next next step callback
 *
 */
handler.getUsers = function(msg, session, next){
    if(!msg.name && !msg.val && !session.uid){
        return next(null, {
            code  : 500,
            error : 'invalid-input'
        });
    }
    var searchId = (msg.name == 'id' || msg.name == 'username' || msg.name == 'email') ? msg.name : 'id';
    var searchVal = typeof msg.val === 'string' ? msg.val : session.uid;
    UserStore.getByAttr(searchId, searchVal, {
        getArray : true
    }, function(e, matches){
        if(e){
            return next(null, {
                code  : 500,
                error : e
            });
        }
        next(null, {
            code    : 200,
            route   : msg.route,
            matches : matches
        });
    });
};

/**
 * Update user profile
 *
 * @param {Object} msg game parameters from client
 * @param {Object} session
 * @param  {Function} next next step callback
 *
 */
handler.setProfile = function(msg, session, next){
    if(!session.uid){
        return next(null, {
            code  : 500,
            error : 'invalid-session'
        });
    }
    UserStore.getByAttr('id', session.uid, false, function(e, user){
        if(e){
            return next(null, {
                code  : 500,
                error : e
            });
        }
        var userObj = {
            id : user.id
        };
        if(msg.email){
            userObj.email = msg.email.trim();
        }
        UserStore.set(userObj, function(e, updatedUser){
            if(e){
                return next(null, {
                    code  : 500,
                    error : e
                });
            }
            next(null, {
                code   : 200,
                route  : msg.route
            });
        });
    });
};

/**
 * Update user profile
 *
 * @param {Object} msg game parameters from client
 * @param {Object} msg.displayName
 * @param {Object} msg.phone
 * @param {Object} msg.avatar
 * @param {Object} msg.gender
 * @param {Object} session
 * @param  {Function} next next step callback
 *
 */
handler.updateProfile = function(msg, session, next) {
    logger.info('[userHandler.updateProfile][Step 0] >> msg: ', msg);
    if(!session.uid){
        return next(null, {
            code  : 500,
            error : 'invalid-session'
        });
    }
    const uid = session.uid;
    const displayName = msg.displayName || '';
    const data = {
        id: uid,
        // firstName: msg.firstName,
        //lastName: msg.lastName,
        phone: msg.phone || '',
        // email: msg.email || '',
        avatar: msg.avatar || '',
        gender: msg.gender || '',
        // birthday: msg.birthday || '',
        displayName: displayName || ''
    }
    logger.info('[usersService.updateProfile][Step 1] >> data ', data);
    this.app.get('usersService').updatePlayerInfo(data, function (err, code, res) {
        if (err || code !== CODE.OK) {
            logger.error('[usersService.updateProfile][Step 2] >> err ', err);
            return next(null, {
                code  : code,
                error : err
            });
        }
        logger.info('[usersService.updateProfile][Step 3] >> res ', res);
        next(null, {
            code    : 200,
            route   : msg.route,
            msg     : res
        });
    });

    // UserStore.getByAttr('id', session.uid, false, function(e, user){
    //     if(e){
    //         return next(null, {
    //             code  : 500,
    //             error : e
    //         });
    //     }
    //     var userObj = {
    //         id : user.id
    //     };
    //     if(msg.email){
    //         userObj.email = msg.email.trim();
    //     }
    //     UserStore.set(userObj, function(e, updatedUser){
    //         if(e){
    //             return next(null, {
    //                 code  : 500,
    //                 error : e
    //             });
    //         }
    //         next(null, {
    //             code   : 200,
    //             route  : msg.route
    //         });
    //     });
    // });
};

/**
 * Update user password
 *
 * @param {Object} msg game parameters from client
 * @param {Object} session
 * @param  {Function} next next step callback
 *
 */
handler.setPassword = function(msg, session, next) {
    logger.info('[userHandler.setPassword][Step 0] >> msg: ', msg);

    if(!session.uid || !msg.old_password || !msg.password) {
        return next(null, {
            code  : 500,
            error : 'invalid-input'
        });
    }

    const playerId = session.uid;
    const dataPost = {
        id: playerId,
        user_id: msg.user_id,
        new_password: msg.password.trim(),
        old_password: msg.old_password.trim()
    }
    this.app.get('usersService').updatePassword(dataPost, function (err, code, res) {
        logger.info('[usersService.updatePassword][Step 1] >> dataPost ', dataPost, ', err ', err, ' -> code ', code, ' -> res ', res);
        if (err) {
            return next(null, {
                code  : 500,
                error : res
            });
        }

        return next(null, {
            code    : code,
            route   : msg.route,
            // msg     : res
        });
    });

    // UserStore.getByAttr(['id', 'password'], [session.uid, msg.oldpassword], false, function(e, user){
    //     if(e){
    //         return next(null, {
    //             code  : 500,
    //             error : e
    //         });
    //     }
    //     var userObj = {
    //         id       : user.id,
    //         password : msg.password.trim()
    //     };
    //     UserStore.set(userObj, function(e, updatedUser){
    //         if(e){
    //             return next(null, {
    //                 code  : 500,
    //                 error : e
    //             });
    //         }
    //         next(null, {
    //             code   : 200,
    //             route  : msg.route
    //         });
    //     });
    // });
};

// handler.setPassword = function(msg, session, next){
//     if(!session.uid || !msg.oldpassword || !msg.password){
//         return next(null, {
//             code  : 500,
//             error : 'invalid-input'
//         });
//     }
//     UserStore.getByAttr(['id', 'password'], [session.uid, msg.oldpassword], false, function(e, user){
//         if(e){
//             return next(null, {
//                 code  : 500,
//                 error : e
//             });
//         }
//         var userObj = {
//             id       : user.id,
//             password : msg.password.trim()
//         };
//         UserStore.set(userObj, function(e, updatedUser){
//             if(e){
//                 return next(null, {
//                     code  : 500,
//                     error : e
//                 });
//             }
//             next(null, {
//                 code   : 200,
//                 route  : msg.route
//             });
//         });
//     });
// };


/**
 * Add a friend to friend list (mặc định ở chế độ chờ đồng ý)
 *
 * @param {Object} msg game parameters from client: uid, fid
 * @param {Object} session
 * @param  {Function} next next step callback
 *
 */
handler.addFriend = async function(msg, session, next) {
    const me = this;
    if(!session.uid) {
        return next(null, {
            code  : 500,
            error : 'invalid-session'
        });
    }
    if(!msg.fid) {
        return next(null, {
            code   : 500,
            error : 'friend-invalid-session'
            // route  : msg.route
        });
    }
    const userId = session.uid;
    // check xem 2 tài khoản này đã là bạn chưa
    const { codeFriend0, resFriend0 } = await new Promise((resolve, reject) => {
        const payload = {
            player_id: userId, // msg.uid,
            f_player_id: msg.fid,
        }
        this.app.rpc.db.dbRemote.checkFriendExist('*', payload, (err, codeFriend0, resFriend0) => {
            if (err) reject(err);
            else resolve({ codeFriend0, resFriend0 });
        });
    });

    logger.info('addFriend >> checkFriendExist: >> codeFriend: ', codeFriend0, ' >> resFriend0: ',resFriend0);
    if (resFriend0) {
        return next(null, {
            code    : 500,
            error   : 'request-has-been-sent',
        });
    }

    this.app.get('friendService').addFriend(userId, msg.fid, function (err, code, res) {
        // logger.info("addFriend >> err: ", err ," -> code: ", code);

        if (code === CODE.NOT_FOUND) {
            return next(null, {
                code    : code,
                error   : res,
            });
        }

        if (code === CODE.FAIL) {
            return next(null, {
                code    : code,
                error   : 'request-has-been-sent',
            });
        }

        return next(null, {
            code    : code,
            route   : msg.route,
            // msg     : res
        });
    });
};

// handler.addFriend = function(msg, session, next){
//     if(!session.uid){
//         return next(null, {
//             code  : 500,
//             error : 'invalid-session'
//         });
//     }
//     if(!msg.friend){
//         return next(null, {
//             code   : 200,
//             route  : msg.route
//         });
//     }
//     UserStore.getByAttr('id', session.uid, {
//         getFullEntity : true
//     }, function(e, user){
//         if(e){
//             return next(null, {
//                 code  : 500,
//                 error : e
//             });
//         }
//         UserStore.getByAttr('id', msg.friend, false, function(e, friend){
//             if(e){
//                 return next(null, {
//                     code  : 500,
//                     error : e
//                 });
//             }
//             user.friends.push({
//                 id       : friend.id,
//                 username : friend.username
//             });
//             UserStore.set({
//                 id      : user.id,
//                 friends : user.friends
//             }, function(e, updatedUser){
//                 if(e){
//                     return next(null, {
//                         code  : 500,
//                         error : e
//                     });
//                 }
//                 next(null, {
//                     code   : 200,
//                     route  : msg.route
//                 });
//             });
//         });
//     });
// };

// xóa yêu cầu kết bạn

/**
 * Hàm hủy yêu cầu kết bạn
 * @param {Object} msg 
 * @param {Object} session 
 * @param {Function} next 
 * @returns 
 */
handler.unAddFriend = function(msg, session, next) {
    if(!session.uid) {
        return next(null, {
            code  : 500,
            error : 'invalid-session'
        });
    }

    if(!msg.fid) {
        return next(null, {
            code   : 500,
            error : 'friend-invalid-session'
        });
    }
    const playerId = session.uid;
    this.app.get('friendService').unAddFriend(playerId, msg.fid, function (err, code, res) {
        if (err) {
            return next(null, {
                code  : 500,
                error : res
            });
        }

        return next(null, {
            code    : code,
            route   : msg.route,
            // msg     : res
        });
    });
};

/**
 * Xác nhận yêu cầu kết bạn (done)
 * @param {Object} msg tham số
 * @param {Object} session session hiện tại
 * @param {Function} next callback
 */
handler.acceptFriend = function(msg, session, next) {
    if(!session.uid) {
        return next(null, {
            code  : 500,
            error : 'invalid-session'
        });
    }

    if(!msg.fid) {
        return next(null, {
            code   : 500,
            error : 'friend-invalid-session'
            // route  : msg.route
        });
    }
    const userId = session.uid;
    this.app.get('friendService').acceptFriend(userId, msg.fid, FRIEND_TYPE.ACCEPTED, function (err, code, res) {
        // logger.info("acceptFriend >> err: ", err ," -> code: ", code, ' -> res: ', res);
        if (err) {
            return next(null, {
                code  : 500,
                error : res
            });
        }

        return next(null, {
            code    : code,
            route   : msg.route,
            // msg     : res
        });
    });
};


/**
 * Từ chối yêu cầu kết bạn (done)
 * @param {Object} msg tham so
 * @param {Object} session session hien tai
 * @param {Function} next callback
 */
handler.rejectFriend = function(msg, session, next) {
    if(!session.uid) {
        return next(null, {
            code  : 500,
            error : 'invalid-session'
        });
    }

    if(!msg.fid) {
        return next(null, {
            code   : 500,
            error : 'friend-invalid-session'
            // route  : msg.route
        });
    }
    const userId = session.uid;
    this.app.get('friendService').rejectFriend(userId, msg.fid, FRIEND_TYPE.REJECTED, function (err, code, res) {

        if (err) {
            return next(null, {
                code  : 500,
                error : res
            });
        }

        return next(null, {
            code    : code,
            route   : msg.route,
            // msg     : res
        });
    });
};


/**
 * Chặn kết bạn (đã là bạn rồi, giờ khóa lại nhưng đối phương không biết) => Done
 * @param {Object} msg tham so
 * @param {Object} session session hien tai
 * @param {Function} next callback
 */
handler.blockFriend = function(msg, session, next) {
    if(!session.uid) {
        return next(null, {
            code  : 500,
            error : 'invalid-session'
        });
    }

    if(!msg.fid) {
        return next(null, {
            code   : 500,
            error : 'friend-invalid-session'
        });
    }
    const userId = session.uid;
    this.app.get('friendService').blockFriend(userId, msg.fid, FRIEND_TYPE.BLOCKED, function (err, code, res) {

        if (err) {
            return next(null, {
                code  : 500,
                error : res
            });
        }

        return next(null, {
            code    : code,
            route   : msg.route,
            // msg     : res
        });
    });
};


/**
 * Hủy Chặn kết bạn (chuyển lại trạng thái là bạn)
 * @param {Object} msg tham so
 * @param {Object} session session hien tai
 * @param {Function} next callback
 */
handler.unBlockFriend = function(msg, session, next) {
    if(!session.uid) {
        return next(null, {
            code  : 500,
            error : 'invalid-session'
        });
    }

    if(!msg.fid) {
        return next(null, {
            code   : 500,
            error : 'friend-invalid-session'
        });
    }

    const userId = session.uid;

    this.app.get('friendService').unBlockFriend(userId, msg.fid, FRIEND_TYPE.ACCEPTED, function (err, code, res) {

        if (err) {
            return next(null, {
                code  : 500,
                error : res
            });
        }

        return next(null, {
            code    : code,
            route   : msg.route,
            // msg     : res
        });
    });
};


/**
 * Hủy kết bạn (done)
 * @param {Object} msg tham so
 * @param {Object} session session hien tai
 * @param {Function} next callback
 */
handler.unFriend = function(msg, session, next) {
    if(!session.uid) {
        return next(null, {
            code  : 500,
            error : 'invalid-session'
        });
    }

    if(!msg.fid) {
        return next(null, {
            code   : 500,
            error : 'friend-invalid-session'
        });
    }

    const userId = session.uid;

    this.app.get('friendService').unFriend(userId, msg.fid, function (err, code, res) {

        if (err) {
            return next(null, {
                code  : 500,
                error : res
            });
        }

        return next(null, {
            code    : code,
            route   : msg.route,
        });
    });
};


/**
 * Hàm lấy danh sách tin nhắn và email của người dùng
 *
 * @param {Object} msg - The message object containing parameters from the client.
 * @param {Object} session - The current user's session object.
 * @param {Function} next - The callback function to be called with the result.
 *
 * If the session is invalid, the callback is invoked with an error code and message.
 * Otherwise, it fetches messages and emails for the user and returns them via the callback.
 */
handler.getMessagesAndEmails = function(msg, session, next) {
    logger.info("[userHandler.getMessagesAndEmails] >> msg: ", msg);
    if(!session.uid) {
        return next(null, {
            code  : 500,
            error : 'invalid-session'
        });
    }
    const userId = session.uid;
    this.app.get('usersService').getMessagesAndEmailsHandler(userId, function (err, res) {
        logger.info("[userHandler.getMessagesAndEmails] >> err: ", err ,' -> res: ', res);
        if (err) {
            return next(null, {
                code  : 500,
                error : res
            });
        }

        return next(null, {
            code    : CODE.OK,
            route   : msg.route,
            msg     : res
        });
    });
};

/**
 * Lấy danh sách thành tích (badges) của người dùng
 *
 * @param {Object} msg - Thông tin từ client (có thể bao gồm filter, page, limit)
 * @param {Object} session - Session của người dùng hiện tại
 * @param {Function} next - Callback function
 * @return {void}
 */
// handler.getBadges = function(msg, session, next) {
//     logger.info("[userHandler.getBadges] >> msg: ", msg);
//     if(!session.uid) {
//         return next(null, {
//             code  : 500,
//             error : 'invalid-session'
//         });
//     }

//     const userId = session.uid;
//     const page = msg.page || 1;
//     const limit = msg.limit || CONSTS.EVENT.PAGESIZE;
//     // const category = msg.category || null; // Lọc theo category nếu có

//     // Chuẩn bị data để gọi service
//     const payload = {
//         userId: userId,
//         page: page,
//         limit: limit,
//         // category: category
//     };

//     this.app.get('usersService').getBadgesHandler(payload, function(err, code, result) {
//         logger.info("[userHandler.getBadges] >> result: ", result, " >> err: ", err, " >> code: ", code);

//         if (err) {
//             return next(null, {
//                 code: 500,
//                 error: 'Error fetching badges'
//             });
//         }

//         return next(null, {
//             code: CODE.OK,
//             route: msg.route,
//             msg: result
//         });
//     });
// };

/**
 * Nhận thưởng từ thành tích (badge)
 *
 * @param {Object} msg - Thông tin từ client (badge_id là bắt buộc)
 * @param {Object} session - Session của người dùng hiện tại
 * @param {Function} next - Callback function
 * @return {void}
 */
handler.claimBadgeReward = function(msg, session, next) {
    logger.info("[userHandler.claimBadgeReward] >> msg: ", msg);
    if(!session.uid) {
        return next(null, {
            code: 500,
            error: 'invalid-session'
        });
    }

    if(!msg.badge_id) {
        return next(null, {
            code: 500,
            error: 'missing-badge-id'
        });
    }

    const userId = session.uid;
    const badgeId = msg.badge_id;

    // Chuẩn bị data để gọi service
    const payload = {
        userId: userId,
        badgeId: badgeId
    };

    this.app.get('usersService').claimBadgeRewardHandler(payload, function(err, code, result) {
        logger.info("[userHandler.claimBadgeReward] >> result: ", result, " >> err: ", err, " >> code: ", code);

        if (err || code !== CODE.OK) {
            return next(null, {
                code: code || 500,
                error: result || 'Error claiming badge reward'
            });
        }

        return next(null, {
            code: CODE.OK,
            route: msg.route,
            msg: result
        });
    });
};

/**
 * Lấy danh sách tất cả các thành tích trong hệ thống
 *
 * @param {Object} msg - Thông tin từ client (có thể bao gồm type)
 * @param {Object} session - Session của người dùng hiện tại
 * @param {Function} next - Callback function
 * @return {void}
 */
handler.getAllBadges = function(msg, session, next) {
    logger.info("[userHandler.getAllBadges] >> msg: ", msg);
    if(!session.uid) {
        return next(null, {
            code: 500,
            error: 'invalid-session'
        });
    }

    // Lấy type từ msg, có thể là một mảng hoặc một giá trị đơn
    const type = msg.type || null; // Có thể là POKE_CAREER hoặc ACHIEVEMENTS
    const userId = session.uid;

    // Chuẩn bị data để gọi service
    const payload = {
        type: type,
        userId: userId
    };

    this.app.get('usersService').getAllBadgesHandler(payload, function(err, code, result) {
        logger.info("[userHandler.getAllBadges] >> result length: ", result?.badges?.length, " >> err: ", err, " >> code: ", code);

        if (err) {
            return next(null, {
                code: 500,
                error: 'Error fetching all badges'
            });
        }

        return next(null, {
            code: CODE.OK,
            route: msg.route,
            msg: result
        });
    });
};

/**
 * Get list of default system avatars
 * 
 * @param {Object} msg - Message from client
 * @param {Object} session - User session
 * @param {Function} next - Callback function
 * @return {void}
 */
handler.getDefaultAvatars = function(msg, session, next) {
    logger.info("[userHandler.getDefaultAvatars] >> msg: ", msg);
    
    if(!session.uid) {
        return next(null, {
            code: 500,
            error: 'invalid-session'
        });
    }

    try {
        const avatars = require('../../../../config/data/avatars.json');
        return next(null, {
            code: CODE.OK,
            route: msg.route,
            data: {
                avatars: avatars
            }
        });
    } catch(err) {
        logger.error("[userHandler.getDefaultAvatars] Error: ", err);
        return next(null, {
            code: 500,
            error: 'Error loading avatars'
        });
    }
};

/**
 * Mark player tutorial as completed
 * 
 * @param {Object} msg - Message from client
 * @param {Object} session - User session
 * @param {Function} next - Callback function
 * @return {void}
 */
handler.completeTutorial = function(msg, session, next) {
    logger.info("[userHandler.completeTutorial] >> msg: ", msg);
    
    if(!session.uid) {
        return next(null, {
            code: 500,
            error: 'invalid-session'
        });
    }

    const playerId = session.uid;
    
    this.app.get('usersService').completeTutorialHandler(playerId, function(err, code, result) {
        logger.info("[userHandler.completeTutorial] >> result: ", result, " >> err: ", err, " >> code: ", code);

        if (err) {
            return next(null, {
                code: 500,
                error: 'Error updating tutorial status'
            });
        }

        return next(null, {
            code: CODE.OK,
            route: msg.route,
            msg: result
        });
    });
};
