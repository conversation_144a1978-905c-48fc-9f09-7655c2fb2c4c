'use strict';

const logger = require('pomelo-logger').getLogger('safebox-log', __filename);
const { models, sequelize } = require('../models');
const { SafeBox, SafeBoxTransaction, Player, Transactions } = models;
const bcrypt = require('bcrypt');
const CODE = require('../consts/code');
const LogTypes = require('../consts/logTypes');
const moment = require('moment');

const SALT_ROUNDS = 10;
const MAX_WRONG_ATTEMPTS = 5;
const LOCK_DURATION_HOURS = 24;

/**
 * SafeBox Service
 * @param {Object} app - Pomelo app instance
 */
module.exports = function(app) {
  return new SafeBoxService(app);
};

/**
 * SafeBox Service Class
 */
class SafeBoxService {
  constructor(app) {
    this.app = app;
  }

  /**
   * Kiểm tra xem người chơi có đủ điều kiện sử dụng két sắt không
   * @param {number} playerId - ID của người chơi
   * @returns {Promise<boolean>} - true nếu đủ điều kiện, false nếu không
   */
  async checkEligibility(playerId) {
    try {
      const player = await Player.findByPk(playerId);
      if (!player) {
        return false;
      }
      logger.info('[safeService.checkEligibility] player: ', player);
      // Kiểm tra điều kiện: cấp 7 hoặc là VIP
      return player.level >= 7 || player.vip_point > 0;
    } catch (error) {
      logger.error('[safeService.checkEligibility] Error checking eligibility:', error);
      return false;
    }
  }

  /**
   * Thiết lập mật khẩu két sắt
   * @param {number} playerId - ID của người chơi
   * @param {string} password - Mật khẩu két sắt
   * @returns {Promise<Object>} - Kết quả thiết lập mật khẩu
   */
  async setupPassword(playerId, password) {
    try {
      // Kiểm tra điều kiện sử dụng két sắt
      const isEligible = await this.checkEligibility(playerId);
      if (!isEligible) {
        return {
          code: CODE.SAFEBOX.INVALID_RULE,
          msg: 'Bạn cần đạt cấp 7 hoặc là VIP để sử dụng két sắt'
        };
      }

      // Kiểm tra xem đã có két sắt chưa
      const existingSafeBox = await SafeBox.findOne({ where: { player_id: playerId } });
      if (existingSafeBox) {

        // kiểm tra xem use_password === 1 thì mới kiểm tra mật khẩu
        if (existingSafeBox.use_password === 1) {
          // const isPasswordValid = await bcrypt.compare(password, existingSafeBox.password_hash);
          // if (!isPasswordValid) {
          //   return {
          //     code: CODE.SAFEBOX.INVALID_PASSWORD,
          //     msg: 'Mật khẩu không đúng'
          //   };
          // }
          return {
            code: CODE.SAFEBOX.ALREADY_SETUP,
            msg: 'Két sắt đã được thiết lập'
          };
        } else {
          // trường hợp chưa set mật khẩu nhưng lại tồn tại thông tin két sắt rồi
          // thì update lại thông tin két sắt
          existingSafeBox.use_password = 1;
          existingSafeBox.password_hash = await bcrypt.hash(password, SALT_ROUNDS);
          await existingSafeBox.save();

          return {
            code: CODE.OK,
            msg: 'Thiết lập mật khẩu két sắt thành công'
          };
        }
        // return {
        //   code: CODE.SAFEBOX.ALREADY_SETUP,
        //   msg: 'Két sắt đã được thiết lập'
        // };
      } // end trường hợp đã tồn tại két sắt

      // Đây là trường hợp chưa có thông tin két sắt thì tiến hành tạo bình thường
      // Hash mật khẩu
      const passwordHash = await bcrypt.hash(password, SALT_ROUNDS);

      // Tạo két sắt mới
      await SafeBox.create({
        player_id: playerId,
        password_hash: passwordHash,
        use_password: 1,
        balance: 0,
        wrong_attempts: 0,
        locked_until: null
      });

      return {
        code: CODE.OK,
        msg: 'Thiết lập mật khẩu két sắt thành công'
      };
    } catch (error) {
      logger.error('Error setting up password:', error);
      return {
        code: CODE.FAIL,
        msg: 'Đã xảy ra lỗi khi thiết lập mật khẩu két sắt'
      };
    }
  }

  /**
   * Xóa mật khẩu két sắt
   * @param {number} playerId - ID của người chơi
   * @param {string} password - Mật khẩu két sắt
   * @returns {Promise<Object>} - Kết quả xóa mật khẩu
   */
  async unsetPassword(playerId, password) {
    try {
      // Kiểm tra xem đã có két sắt chưa
      let existingSafeBox = await SafeBox.findOne({ where: { player_id: playerId } });
      if (!existingSafeBox) {
        // Tạo két sắt mới
        let passwordHash = null;
        let isUsePassword = 0;
        if (password) {
          passwordHash = await bcrypt.hash(password, SALT_ROUNDS);
          isUsePassword = 1;
        }
        existingSafeBox = await SafeBox.create({
          player_id: playerId,
          password_hash: passwordHash,
          use_password: isUsePassword,
          balance: 0,
          wrong_attempts: 0,
          locked_until: null
        });

        return {
          code: CODE.SAFEBOX.NOT_FOUND,
          msg: 'Bạn chưa thiết lập két sắt'
        };
      }

      logger.info('[safeService.unsetPassword][1] existingSafeBox: ', existingSafeBox);

      // check if use_password === 0 then callback
      if (existingSafeBox.use_password === 0) {
        return {
          code: CODE.OK,
          msg: 'Bạn đang không đặt mật khẩu cho két sắt'
        };
      }

      // Kiểm tra mật khẩu
      if (existingSafeBox.use_password === 1) {
        const isPasswordValid = await bcrypt.compare(password, existingSafeBox.password_hash);
        if (!isPasswordValid) {
          return {
            code: CODE.SAFEBOX.INVALID_PASSWORD,
            msg: 'Mật khẩu không đúng'
          };
        }
      } // end check use_password === 1 mới kiểm tra

      // Xóa két sắt
      // await existingSafeBox.destroy();
      // không xóa item két sắt mà chỉ cần reset lại mật khẩu
      existingSafeBox.password_hash = null;
      existingSafeBox.use_password = 0;
      existingSafeBox.wrong_attempts = 0;
      existingSafeBox.locked_until = null;
      await existingSafeBox.save();

      return {
        code: CODE.OK,
        msg: 'Xóa mật khẩu két sắt thành công'
      };
    } catch (error) {
      logger.error('Error unsetting password:', error, ' -> message: ', error.message, ' -> stack: ', error.stack);
      return {
        code: CODE.FAIL,
        msg: 'Đã xảy ra lỗi khi xóa mật khẩu két sắt'
      };
    }
  }

  /**
   * Thay đổi mật khẩu két sắt
   * @param {number} playerId - ID của người chơi
   * @param {string} oldPassword - Mật khẩu cũ
   * @param {string} newPassword - Mật khẩu mới
   * @returns {Promise<Object>} - Kết quả thay đổi mật khẩu
   */
  async changePassword(playerId, oldPassword, newPassword) {
    try {
      // Lấy thông tin két sắt
      const safeBox = await SafeBox.findOne({ where: { player_id: playerId } });
      if (!safeBox) {
        return {
          code: CODE.FAIL,
          msg: 'Bạn chưa thiết lập két sắt'
        };
      }

      // Kiểm tra két có bị khóa không
      if (safeBox.locked_until && moment().isBefore(moment(safeBox.locked_until))) {
        const unlockTime = moment(safeBox.locked_until).format('HH:mm:ss DD/MM/YYYY');
        return {
          code: CODE.FAIL,
          msg: `Két sắt đã bị khóa do nhập sai mật khẩu quá nhiều lần. Vui lòng thử lại sau ${unlockTime}`
        };
      }

      // kiểm tra xem có sử dụng mật khẩu không đã (use_password === 0)
      if (safeBox.use_password === 0) {
        return {
          code: CODE.SAFEBOX.NOT_SETUP_PASSWORD,
          msg: 'Bạn chưa thiết lập mật khẩu cho két sắt'
        };
      }

      // Kiểm tra mật khẩu cũ
      const isPasswordValid = await bcrypt.compare(oldPassword, safeBox.password_hash);
      if (!isPasswordValid) {
        // Tăng số lần nhập sai
        safeBox.wrong_attempts += 1;
        
        // Nếu nhập sai quá nhiều lần, khóa két
        if (safeBox.wrong_attempts >= MAX_WRONG_ATTEMPTS) {
          safeBox.locked_until = moment().add(LOCK_DURATION_HOURS, 'hours').toDate();
          safeBox.wrong_attempts = 0;
          await safeBox.save();
          
          return {
            code: CODE.FAIL,
            msg: `Két sắt đã bị khóa do nhập sai mật khẩu quá nhiều lần. Vui lòng thử lại sau ${LOCK_DURATION_HOURS} giờ`
          };
        }
        
        await safeBox.save();
        
        return {
          code: CODE.FAIL,
          msg: `Mật khẩu không đúng. Còn ${MAX_WRONG_ATTEMPTS - safeBox.wrong_attempts} lần thử`
        };
      }

      // Reset số lần nhập sai
      safeBox.wrong_attempts = 0;
      
      // Hash mật khẩu mới
      const passwordHash = await bcrypt.hash(newPassword, SALT_ROUNDS);
      safeBox.password_hash = passwordHash;
      
      await safeBox.save();

      return {
        code: CODE.OK,
        msg: 'Thay đổi mật khẩu két sắt thành công'
      };
    } catch (error) {
      logger.error('Error changing password:', error);
      return {
        code: CODE.FAIL,
        msg: 'Đã xảy ra lỗi khi thay đổi mật khẩu két sắt'
      };
    }
  }

  /**
   * Gửi tiền vào két sắt
   * @param {number} playerId - ID của người chơi
   * @param {number} amount - Số tiền muốn gửi
   * @returns {Promise<Object>} - Kết quả gửi tiền
   */
  async deposit(playerId, amount) {
    logger.info('[safeService.deposit][0] playerId: ', playerId, ' -> amount: ', amount);
    try {
      // Kiểm tra số tiền hợp lệ
      if (!amount || amount <= 0) {
        return {
          code: CODE.FAIL,
          msg: 'Số tiền không hợp lệ'
        };
      }

      // Lấy thông tin két sắt
      let safeBox = await SafeBox.findOne({ where: { player_id: playerId } });
      logger.info('[safeService.deposit][1] safeBox: ', safeBox);
      if (!safeBox) {
        // Tạo két sắt mới
        safeBox = await SafeBox.create({
          player_id: playerId,
          password_hash: null,
          use_password: 0,
          balance: 0,
          wrong_attempts: 0,
          locked_until: null
        });

        // return {
        //   code: CODE.SAFEBOX.NOT_FOUND,
        //   msg: 'Bạn chưa thiết lập két sắt'
        // };
      }

      // Lấy thông tin người chơi
      const player = await Player.findByPk(playerId);
      logger.info('[safeService.deposit][2] player: ', player);
      if (!player) {
        return {
          code: CODE.NOT_FOUND,
          msg: 'Không tìm thấy thông tin người chơi'
        };
      }

      // Kiểm tra số dư
      if (player.balance < amount) {
        return {
          code: CODE.SAFEBOX.NOT_ENOUGH_MONEY,
          msg: 'Số dư không đủ để thực hiện giao dịch'
        };
      }

      // Thực hiện giao dịch trong transaction để đảm bảo tính nhất quán
      const result = await sequelize.transaction(async (t) => {
        // Trừ tiền từ tài khoản chính
        const beforeBalance = player.balance;
        player.balance -= amount;
        await player.save({ transaction: t });

        // Cộng tiền vào két sắt
        safeBox.balance += amount;
        await safeBox.save({ transaction: t });

        // Ghi log giao dịch két sắt
        await SafeBoxTransaction.create({
          player_id: playerId,
          type: 'DEPOSIT',
          amount: amount,
          status: 'SUCCESS',
          note: 'Gửi tiền vào két sắt'
        }, { transaction: t });

        // Ghi log giao dịch chung
        await Transactions.create({
          player_id: playerId,
          amount: -amount, // Số âm vì trừ tiền
          before_balance: beforeBalance,
          after_balance: player.balance,
          type: LogTypes.SAFE_BOX,
          action: 'DEPOSIT',
          reference_id: safeBox.id,
          reference_type: 'SAFE_BOX',
          description: 'Gửi tiền vào két sắt'
        }, { transaction: t });

        return {
          code: CODE.OK,
          msg: 'Gửi tiền vào két sắt thành công',
          data: {
            amount: amount,
            balance: player.balance,
            safeBoxBalance: safeBox.balance
          }
        };
      });

      return result;
    } catch (error) {
      logger.error('[safeService.deposit][3] Error depositing money:', error, ' -> message: ', error.message, ' -> stack: ', error.stack);
      return {
        code: CODE.FAIL,
        msg: 'Đã xảy ra lỗi khi gửi tiền vào két sắt'
      };
    }
  }

  /**
   * Rút tiền từ két sắt
   * @param {number} playerId - ID của người chơi
   * @param {string} password - Mật khẩu két sắt
   * @param {number} amount - Số tiền muốn rút
   * @returns {Promise<Object>} - Kết quả rút tiền
   */
  async withdraw(playerId, password, amount) {
    logger.info('[safeService.withdraw][0] playerId: ', playerId, ' -> password: ', password, ' -> amount: ', amount);
    try {
      // Kiểm tra số tiền hợp lệ
      if (!amount || amount <= 0) {
        return {
          code: CODE.FAIL,
          msg: 'Số tiền không hợp lệ'
        };
      }

      // Lấy thông tin két sắt
      let safeBox = await SafeBox.findOne({ where: { player_id: playerId } });
      logger.info('[safeService.withdraw][1] safeBox: ', safeBox);
      if (!safeBox) {

        // Tạo két sắt mới
        let passwordHash = null;
        let isUsePassword = 0;
        if (password) {
          passwordHash = await bcrypt.hash(password, SALT_ROUNDS);
          isUsePassword = 1;
        }
        safeBox = await SafeBox.create({
          player_id: playerId,
          password_hash: passwordHash,
          use_password: isUsePassword,
          balance: 0,
          wrong_attempts: 0,
          locked_until: null
        });

        // return {
        //   code: CODE.SAFEBOX.NOT_FOUND,
        //   msg: 'Bạn chưa thiết lập két sắt'
        // };
      }

      // Kiểm tra két có bị khóa không
      if (safeBox.locked_until && moment().isBefore(moment(safeBox.locked_until))) {
        const unlockTime = moment(safeBox.locked_until).format('HH:mm:ss DD/MM/YYYY');
        return {
          code: CODE.SAFEBOX.LOCKED_TOO_MANY_TIMES,
          msg: `Két sắt đã bị khóa do nhập sai mật khẩu quá nhiều lần. Vui lòng thử lại sau ${unlockTime}`
        };
      }

      // kiểm tra xem tài khoản này có sử dụng mật khẩu để truy cập két không
      if (safeBox.use_password === 1) {
        // Kiểm tra mật khẩu
        const isPasswordValid = await bcrypt.compare(password, safeBox.password_hash);
        logger.info('[safeService.withdraw][2] isPasswordValid: ', isPasswordValid);
        if (!isPasswordValid) {
          // Tăng số lần nhập sai
          safeBox.wrong_attempts += 1;
          
          // Nếu nhập sai quá nhiều lần, khóa két
          if (safeBox.wrong_attempts >= MAX_WRONG_ATTEMPTS) {
            safeBox.locked_until = moment().add(LOCK_DURATION_HOURS, 'hours').toDate();
            safeBox.wrong_attempts = 0;
            await safeBox.save();
            
            // Ghi log giao dịch két sắt thất bại
            await SafeBoxTransaction.create({
              player_id: playerId,
              type: 'WITHDRAW',
              amount: amount,
              status: 'FAILED',
              note: 'Nhập sai mật khẩu quá nhiều lần, két sắt đã bị khóa'
            });
            
            return {
              code: CODE.FAIL,
              msg: `Két sắt đã bị khóa do nhập sai mật khẩu quá nhiều lần. Vui lòng thử lại sau ${LOCK_DURATION_HOURS} giờ`
            };
          }
          
          await safeBox.save();
          
          // Ghi log giao dịch két sắt thất bại
          await SafeBoxTransaction.create({
            player_id: playerId,
            type: 'WITHDRAW',
            amount: amount,
            status: 'FAILED',
            note: `Nhập sai mật khẩu (lần ${safeBox.wrong_attempts}/${MAX_WRONG_ATTEMPTS})`
          });
          
          return {
            code: CODE.FAIL,
            msg: `Mật khẩu không đúng. Còn ${MAX_WRONG_ATTEMPTS - safeBox.wrong_attempts} lần thử`
          };
        } // end check password wrong
      } // end check use_password === 1

      // Reset số lần nhập sai
      safeBox.wrong_attempts = 0;
      await safeBox.save();

      // Kiểm tra số dư két sắt
      if (safeBox.balance < amount) {
        // Ghi log giao dịch két sắt thất bại
        await SafeBoxTransaction.create({
          player_id: playerId,
          type: 'WITHDRAW',
          amount: amount,
          status: 'FAILED',
          note: 'Số dư két sắt không đủ'
        });
        
        return {
          code: CODE.FAIL,
          msg: 'Số dư két sắt không đủ để thực hiện giao dịch'
        };
      }

      // Lấy thông tin người chơi
      const player = await Player.findByPk(playerId);
      logger.info('[safeService.withdraw][3] player: ', player);
      if (!player) {
        return {
          code: CODE.NOT_FOUND,
          msg: 'Không tìm thấy thông tin người chơi'
        };
      }

      // Thực hiện giao dịch trong transaction để đảm bảo tính nhất quán
      const result = await sequelize.transaction(async (t) => {
        // Trừ tiền từ két sắt
        safeBox.balance -= amount;
        await safeBox.save({ transaction: t });

        // Cộng tiền vào tài khoản chính
        const beforeBalance = player.balance;
        player.balance += amount;
        await player.save({ transaction: t });

        // Ghi log giao dịch két sắt
        await SafeBoxTransaction.create({
          player_id: playerId,
          type: 'WITHDRAW',
          amount: amount,
          status: 'SUCCESS',
          note: 'Rút tiền từ két sắt'
        }, { transaction: t });

        // Ghi log giao dịch chung
        await Transactions.create({
          player_id: playerId,
          amount: amount, // Số dương vì cộng tiền
          before_balance: beforeBalance,
          after_balance: player.balance,
          type: LogTypes.SAFE_BOX,
          action: 'WITHDRAW',
          reference_id: safeBox.id,
          reference_type: 'SAFE_BOX',
          description: 'Rút tiền từ két sắt'
        }, { transaction: t });

        return {
          code: CODE.OK,
          msg: 'Rút tiền từ két sắt thành công',
          data: {
            amount: amount,
            balance: player.balance,
            safeBoxBalance: safeBox.balance
          }
        };
      });

      return result;
    } catch (error) {
      logger.error('[safeService.withdraw][4] Error withdrawing money:', error, ' -> message: ', error.message, ' -> stack: ', error.stack);
      return {
        code: CODE.FAIL,
        msg: 'Đã xảy ra lỗi khi rút tiền từ két sắt'
      };
    }
  }

  /**
   * Lấy số dư két sắt
   * @param {number} playerId - ID của người chơi
   * @param {string} password - Mật khẩu két sắt (có thể null nếu chỉ kiểm tra tồn tại)
   * @returns {Promise<Object>} - Thông tin số dư két sắt
   */
  async getBalance(playerId, password = null) {
    logger.info('[safeService.getBalance][0] playerId: ', playerId, ' -> password: ', password);
    try {
      // Lấy thông tin két sắt
      let safeBox = await SafeBox.findOne({ where: { player_id: playerId } });
      logger.info('[safeService.getBalance][1] safeBox: ', safeBox);
      if (!safeBox) {

        // Tạo két sắt mới
        let passwordHash = null;
        let isUsePassword = 0;
        if (password) {
          passwordHash = await bcrypt.hash(password, SALT_ROUNDS);
          isUsePassword = 1;
        }

        safeBox = await SafeBox.create({
          player_id: playerId,
          password_hash: passwordHash,
          use_password: isUsePassword,
          balance: 0,
          wrong_attempts: 0,
          locked_until: null
        });

        // return {
        //   code: CODE.SAFEBOX.NOT_FOUND,
        //   msg: 'Bạn chưa thiết lập két sắt'
        // };
      }

      // Nếu không cung cấp mật khẩu, chỉ trả về thông tin cơ bản, trả thêm balance
      if (!password) {
        return {
          code: CODE.OK,
          msg: 'Thông tin két sắt',
          data: {
            hasSafeBox: true,
            isLocked: safeBox.locked_until && moment().isBefore(moment(safeBox.locked_until)),
            lockedUntil: safeBox.locked_until,
            balance: safeBox.balance || 0,
            isAuthenticated: safeBox.use_password === 1
          }
        };
      }

      // Kiểm tra két có bị khóa không
      if (safeBox.locked_until && moment().isBefore(moment(safeBox.locked_until))) {
        const unlockTime = moment(safeBox.locked_until).format('HH:mm:ss DD/MM/YYYY');
        return {
          code: CODE.SAFEBOX.LOCKED_TOO_MANY_TIMES,
          msg: `Két sắt đã bị khóa do nhập sai mật khẩu quá nhiều lần. Vui lòng thử lại sau ${unlockTime}`
        };
      }

      // Kiểm tra mật khẩu
      const isPasswordValid = await bcrypt.compare(password, safeBox.password_hash);
      if (!isPasswordValid) {
        // Tăng số lần nhập sai
        safeBox.wrong_attempts += 1;
        
        // Nếu nhập sai quá nhiều lần, khóa két
        if (safeBox.wrong_attempts >= MAX_WRONG_ATTEMPTS) {
          safeBox.locked_until = moment().add(LOCK_DURATION_HOURS, 'hours').toDate();
          safeBox.wrong_attempts = 0;
          await safeBox.save();
          
          return {
            code: CODE.SAFEBOX.LOCKED_TOO_MANY_TIMES,
            msg: `Két sắt đã bị khóa do nhập sai mật khẩu quá nhiều lần. Vui lòng thử lại sau ${LOCK_DURATION_HOURS} giờ`
          };
        }
        
        await safeBox.save();
        
        return {
          code: CODE.SAFEBOX.INVALID_PASSWORD,
          msg: `Mật khẩu không đúng. Còn ${MAX_WRONG_ATTEMPTS - safeBox.wrong_attempts} lần thử`
        };
      }

      // Reset số lần nhập sai
      safeBox.wrong_attempts = 0;
      await safeBox.save();

      return {
        code: CODE.OK,
        msg: 'Thông tin két sắt',
        data: {
          balance: safeBox.balance || 0,
          isAuthenticated: safeBox.use_password === 1
        }
      };
    } catch (error) {
      logger.error('[safeService.getBalance][5] Error getting safe box balance:', error, ' -> message: ', error.message, ' -> stack: ', error.stack);
      return {
        code: CODE.FAIL,
        msg: 'Đã xảy ra lỗi khi lấy thông tin két sắt'
      };
    }
  }

  /**
   * Lấy lịch sử giao dịch két sắt
   * @param {number} playerId - ID của người chơi
   * @param {string} password - Mật khẩu két sắt
   * @param {number} page - Trang hiện tại
   * @param {number} limit - Số lượng bản ghi mỗi trang
   * @returns {Promise<Object>} - Lịch sử giao dịch két sắt
   */
  async getTransactions(playerId, password, page = 1, limit = 10) {
    logger.info('[safeService.getTransactions][0] playerId: ', playerId, ' -> password: ', password, ' -> page: ', page, ' -> limit: ', limit);
    try {
      // Lấy thông tin két sắt
      const safeBox = await SafeBox.findOne({ where: { player_id: playerId } });

      logger.info('[safeService.getTransactions][1] safeBox: ', safeBox);

      if (!safeBox) {
        return {
          code: CODE.SAFEBOX.NOT_FOUND,
          msg: 'Bạn chưa thiết lập két sắt'
        };
      }

      // Kiểm tra két có bị khóa không
      if (safeBox.locked_until && moment().isBefore(moment(safeBox.locked_until))) {
        const unlockTime = moment(safeBox.locked_until).format('HH:mm:ss DD/MM/YYYY');
        return {
          code: CODE.SAFEBOX.LOCKED_TOO_MANY_TIMES,
          msg: `Két sắt đã bị khóa do nhập sai mật khẩu quá nhiều lần. Vui lòng thử lại sau ${unlockTime}`
        };
      }

      // Kiểm tra mật khẩu
      const isPasswordValid = await bcrypt.compare(password, safeBox.password_hash);
      if (!isPasswordValid) {
        // Tăng số lần nhập sai
        safeBox.wrong_attempts += 1;
        
        // Nếu nhập sai quá nhiều lần, khóa két
        if (safeBox.wrong_attempts >= MAX_WRONG_ATTEMPTS) {
          safeBox.locked_until = moment().add(LOCK_DURATION_HOURS, 'hours').toDate();
          safeBox.wrong_attempts = 0;
          await safeBox.save();
          
          return {
            code: CODE.SAFEBOX.LOCKED_TOO_MANY_TIMES,
            msg: `Két sắt đã bị khóa do nhập sai mật khẩu quá nhiều lần. Vui lòng thử lại sau ${LOCK_DURATION_HOURS} giờ`
          };
        }
        
        await safeBox.save();
        
        return {
          code: CODE.SAFEBOX.INVALID_PASSWORD,
          msg: `Mật khẩu không đúng. Còn ${MAX_WRONG_ATTEMPTS - safeBox.wrong_attempts} lần thử`
        };
      } // end check password

      // Reset số lần nhập sai
      safeBox.wrong_attempts = 0;
      await safeBox.save();

      // Tính offset
      const offset = (page - 1) * limit;

      // Lấy lịch sử giao dịch
      const { count, rows } = await SafeBoxTransaction.findAndCountAll({
        where: { player_id: playerId },
        order: [['created_at', 'DESC']],
        limit,
        offset
      });

      logger.info('[safeService.getTransactions][2] count: ', count, ' -> rows: ', rows);

      return {
        code: CODE.OK,
        msg: 'Lịch sử giao dịch két sắt',
        data: {
          total: count,
          page,
          limit,
          totalPages: Math.ceil(count / limit),
          transactions: rows
        }
      };
    } catch (error) {
      logger.error('[safeService.getTransactions][3] Error getting safe box transactions:', error, ' -> message: ', error.message, ' -> stack: ', error.stack);
      return {
        code: CODE.FAIL,
        msg: 'Đã xảy ra lỗi khi lấy lịch sử giao dịch két sắt'
      };
    }
  }

  /**
   * hàm kiểm tra trạng thái của người chơi đã thiết lập két sắt hay chưa, có rồi thì có thể trả về số tiền đi kèm
   * @param {Number} playerId 
   * @returns 
   */
  async checkSafeBoxStatus(playerId) {
    try {
      let safeBox = await SafeBox.findOne({ where: { player_id: playerId } });
      if (!safeBox) {

        // Tạo két sắt mới
        safeBox = await SafeBox.create({
          player_id: playerId,
          password_hash: null,
          use_password: 0,
          balance: 0,
          wrong_attempts: 0,
          locked_until: null
        });

        // return {
        //   code: CODE.SAFEBOX.NOT_FOUND,
        //   msg: 'Bạn chưa thiết lập két sắt'
        // };
      }

      return {
        code: CODE.OK,
        msg: 'Bạn đã thiết lập két sắt',
        data: {
          balance: safeBox.balance,
          isAuthenticated: safeBox.use_password === 1
        }
      };
    } catch (error) {
      logger.error('[safeService.checkSafeBoxStatus] Error checking safe box status:', error, ' -> message: ', error.message, ' -> stack: ', error.stack);
      return {
        code: CODE.FAIL,
        msg: 'Đã xảy ra lỗi khi kiểm tra trạng thái thiết lập mật khẩu két'
      };
    }
  } // end checkSafeBoxStatus
} 