var logger              = require('pomelo-logger').getLogger('friend-log', __filename);
var CONSTS              = require('../consts/consts');
const FRIEND_TYPE       = require('../consts/friendType');
const CODE              = require('../consts/code');
var friendDao           = require('../dao/friendDao');
var userDao             = require('../dao/userDao');
var async               = require('async');
var _                   = require('underscore');
const { FriendCusDTO }  = require('../domain/entity/friend');
const { PlayerLiteDTO } = require('../domain/entity/player');
const LogTypes          = require('../consts/logTypes');
const LogBoards         = require('../consts/logBoards');
var messageService      = require('./messageService');
var utils               = require('../util/utils');

var FriendService = function(app){
    this.app    = app;
    this.prefix = "FRIEND:";
};

module.exports = FriendService;


FriendService.prototype.handleGetFriends = function (uid, type, page, limit, cb) {
    logger.info('[FriendService.handleGetFriends][Step 0] handleGetFriends with uid: ', uid, ' type: ', type, ' page: ', page, ' limit: ', limit);
    const me        = this;
    const pageSize  = limit || CONSTS.EVENT.PAGESIZE;
    var start       = 0;
    var usersMap    = {};
    var usersRes    = [];

    if(page > 1) {
        start = (page - 1) * pageSize;
    }

    let payload = {};

    async.waterfall([
        // function(callback) {
        //     // Step 1: get player info from database
        //     // ------------------------------------------------------------------------
        //     // userDao.getOpenUserByUid(uid, callback);
        //     me.app.rpc.db.dbRemote.getPlayerByUserId('*', uid, (err, codePlayer, resPlayer) => {
        //         logger.info('[Step 1] handleGetFriends >> getPlayerByUserId >> err: ', err, ' >> codePlayer: ', codePlayer, ' >> resPlayer: ', resPlayer);
        //         // if (err) reject(err);
        //         // else resolve({ codePlayer, resPlayer });
        //         callback(null, resPlayer);
        //     });
        // },
        // function(data, callback) {

        // Step 1: get list friends from database by id and type
        // ------------------------------------------------------------------------
        function(callback) {
            payload['uid'] = uid; // uuid của user
            
            switch (type) {
                // lấy danh sách lời mời kết bạn (là người khác gửi lời mới tới mình) (C)
                case 1:
                    payload['page'] = page;
                    payload['limit'] = pageSize;
                    // friend request , khong chia page
                    // friendDao.getActiveFriendsByUid(data.oid, callback);
                    me.app.rpc.db.dbRemote.getInviteFriends('*', payload, (err, code, result) => {
                        if (err) {
                            logger.error('[Case 1][Error][handleGetFriends.getInviteFriends] Error:', err);
                            cb(true, err);
                        } else {
                            logger.info('[Case 1][Result][handleGetFriends.getInviteFriends] Result:', JSON.stringify(result));
                            callback(null, result);
                        }
                    });
                    break;

                // lấy danh sách bạn bè của tôi (đã được chấp nhận) là bạn bè của nhau rồi
                case 2:
                    payload['page'] = page;
                    payload['limit'] = pageSize;
                    // all friend , chia page
                    // friendDao.getFriendsByUid(data.oid, start, pageSize, callback);
                    me.app.rpc.db.dbRemote.getFriends('*', payload, (err, code, result) => {
                        if (err) {
                            logger.error('[Case 2][Error][handleGetFriends.getFriends] Error:', err);
                            cb(true, err);
                        } else {
                            logger.info('[Case 2][Result][handleGetFriends.getFriends] Result:', result);
                            callback(null, result);
                        }
                    });
                    break;

                // Danh sách theo dõi (B) (người mình đã gửi lời mời kết bạn nhưng chưa phản hồi hoặc đã từ chối)
                case 3:
                    payload['page'] = page;
                    payload['limit'] = pageSize;
                    // all friend , chia page
                    // friendDao.getFriendsByUid(data.oid, start, pageSize, callback);
                    me.app.rpc.db.dbRemote.getFollowFriends('*', payload, (err, code, result) => {
                        if (err) {
                            logger.error('[Case 3][Error][handleGetFriends.getFollowFriends] Error:', err);
                            cb(true, err);
                        } else {
                            logger.info('[Case 3][Result][handleGetFriends.getFollowFriends] Result:', result);
                            callback(null, result);
                        }
                    });
                    break;

                // danh sách đen (nhóm người chơi đã bị chặn)
                case 4:
                    payload['page'] = page;
                    payload['limit'] = pageSize;
                    me.app.rpc.db.dbRemote.getBlockFriends('*', payload, (err, code, result) => {
                        if (err) {
                            logger.error('[Case 4][Error][handleGetFriends.getBlockFriends] Error:', err);
                            cb(true, err);
                        } else {
                            logger.info('[Case 4][Result][handleGetFriends.getBlockFriends] Result:', result);
                            callback(null, result);
                        }
                    });
                    break;

                case 10:
                    logger.info('[FriendService.handleGetFriends][Step 10] getAllFriends with uid: ', uid, ' -> payload: ', payload);
                    // online friend , khong chia page
                    // friendDao.getAllFriendsByUid(data.oid, callback);
                    me.app.rpc.db.dbRemote.getAllFriends('*', payload, (err, code, result2) => {
                        if (err) {
                            logger.error('[Case 10][Error][handleGetFriends.getAllFriends] Error:', err);
                            cb(true, err);
                        } else {
                            logger.info('[Case 10][Result][handleGetFriends.getAllFriends] Result:', result2, ' -> code: ', code);
                            const reResult = {
                                friends: result2,
                                totalItems: result2.length,
                                totalPages: 1,
                                currentPage: 1,
                            }
                            logger.info('[Case 10][Result][handleGetFriends.getAllFriends] Result:', result2, ' -> reResult: ', reResult);
                            callback(null, reResult);
                        }
                    });
                    break;
            }
            // if (type == 2){
            //     friendDao.getActiveFriendsByUid(data.oid, callback);
            // }else {
            //     friendDao.getFriendsByUid(data.oid, start, pageSize, callback);
            // }
        },
        // function(listFriend, callback) {
        //     // Step 3: check result and return to client
        //     // ------------------------------------------------------------------------
        //     logger.info("list Friend from db: ", listFriend);
        //     // var dataRes = [];
        //     // if (listFriend.total > 0) {
        //     //     dataRes = listFriend;
        //     // }
        //     // callback(null, dataRes);
        //     callback(null, listFriend);
        // },
        // Step 2: check users online in game from data list friends
        // ------------------------------------------------------------------------
        function (data, callback) {
            logger.info("[Step 2] handleGetFriends list data friend callback : ", data, " => data.totalItems: ", data.totalItems, ' -> type: ', type);
            if (data.totalItems > 0) {
                logger.info("Có dữ liệu bạn bè online >> thực hiện check user friend online or offline > uid: ", uid);
                me.__checkAndGetFriendsOnline(uid, type, data, callback);
            }else{
                logger.info("Không có bạn bè nào online >> callback luôn");
                callback(null, data);
            }
        },
        // Step 3: get list user_id info from coin remote, get balance, full_name, username, avatar
        // ------------------------------------------------------------------------
        function (data, callback) {
            logger.info("[Step 3] handleGetFriends data User Online/Offline: ", data, ' -> type: ', type);
            let listUserIds = []
            if (type !== 1) {
                listUserIds = _.pluck(data.friends, 'f_player_id');
            } else {
                listUserIds = _.pluck(data.friends, 'player_id');
            }
            logger.info("[Step 3] handleGetFriends data User Online/Offline >> listUserIds: ", listUserIds);
            // call rpc coin remote get list user_id info
            // me.app.rpc.coin.coinRemote.getAccountInfoByUserIds('*', { userIds: listUserIds }, (err, code, response) => {
            me.app.rpc.authen.authenRemote.getUserInfoByIds('*', { ids: listUserIds }, (err, code, response) => {
                logger.info("[Step 2.1] handleGetFriends >> getUserInfoByIds ", response, " >> err: ", err, " >> code: ", code);
                if (!response.success) {
                    logger.error('2.1.handleGetFriends Error:', err);
                    cb(true, err);
                } else {
                    logger.info('2.1.handleGetFriends Result:', response);
                    // callback(null, { friends: data?.friends, users: response?.items });
                    callback(null, { 
                        friends: data?.friends, 
                        users: response?.items,
                        totalItems: data.totalItems,
                        totalPages: data?.totalPages ?? 1,
                        currentPage: data?.currentPage ?? 1,
                    });
                }
            });
        }
    ], function (err, result) {
        // Step 4: show list friend, reformat data and return to client
        logger.info("[Step 4] handleGetFriends end function show list friend: ", result, ' -> type: ', type);

        let _result = [];

        _.each(result.friends, function (item) {
            let itemInfo = {}
            logger.info("[Step 4.1] handleGetFriends end function show list item: " ,item ," => item.player: ", item?.player, ' -> type: ', type);
            if (type !== 1) {
                itemInfo = result.users.find(user => user.id === item.f_player_id);
            } else {
                itemInfo = result.users.find(user => user.id === item.player_id);
            }
            
            // const itemInfo = result.users.find(user => user.uid === item.f_player_id);
            logger.info("[Step 4.1] handleGetFriends end function show list itemInfo: ", itemInfo);
            // logger.info("[Step 4.1] handleGetFriends end function show list item: ", JSON.stringify(item));

            item.balance = item?.player?.balance || 0;  // itemInfo?.balance || 0;
            item.full_name = itemInfo?.full_name || '';
            // item.nick_name = itemInfo?.name || '';
            item.gender = itemInfo?.gender || '';
            item.is_online = item?.isOnline || false;
            if (type === 1) {
                // logger.info("[Step 4.11] handleGetFriends end function show list item: ", item?.player_invite?.uid);
                item.uid = item?.player_invite?.uid || 0;
                item.user_id = item?.uid || 0;
                item.nick_name = itemInfo?.username || '';
            } else {
                // logger.info("[Step 4.12] handleGetFriends end function show list item: ", item?.player?.uid);
                // item.user_id = itemInfo?.uid || 0;
                item.uid = item?.player?.uid || 0; // || itemInfo?.uid || 0;
            }

            item.avatar = item?.player?.avatar || '1'; // || itemInfo?.avatar || '1';
            item.id = itemInfo?.id || 0;
            item.type = type;

            const reItem = new PlayerLiteDTO(item);
            _result.push(reItem);
        });

        logger.info("[Step 4.1] handleGetFriends end function show list _result: ", _result);

        cb(null, {
            friends: _result,
            totalItems: result?.totalItems ?? 0,
            totalPages: result?.totalPages ?? 1,
            currentPage: result?.currentPage ?? 1,
        });
        return;
    });

};


FriendService.prototype.__checkAndGetFriendsOnline = function (uid, type, data, cb) {

    var me          = this;
    // var type        = 2; // facebook
    var usersMap    = {};
    var usersRes    = [];

    async.waterfall([
        function(callback) {
            // me.app.rpc.manager.userRemote.getUsersCacheByType(null, type, function (res) {
            me.app.rpc.manager.userRemote.getUsersCache(null, function (res) {
                logger.info("getUsersCache in friendServices >> res: ", res, " -> uid: ", uid);

                _.each(res, function(item) {
                    if (item.uid != uid){
                        usersMap[item.uid] = item;
                    }
                });

                logger.info("getUsersCache convert to hashmap: ", usersMap);

                callback(null, usersMap);
            });

        },
        function(usersMap, callback) {

            logger.info("getUsersCache list friends data: ", data);

            _.each(data.friends, function (_ite) {
                // const itemType = _ite.type || 0;
                logger.info("MMM >> uid: ", uid, " => _ite: ", _ite, ' -> type: ', type);

                switch(type) {
                    // danh sách gửi lời mời kết bạn đến mình
                    case 1:
                        if (_ite.player_id != uid) {
                            var suffixes = usersMap[_ite.player_id];
                            logger.info("Type: ", type, " -> MMM >> suffixes: ", suffixes);
                            if (!!suffixes) {
                                _ite.isOnline = true;
                            } else {
                                _ite.isOnline = false;
                            }
                            usersRes.push(_ite);
                        }
                        break;
                    // danh sách theo dõi
                    case 3:
                        if (_ite.f_player_id != uid) {
                            var suffixes = usersMap[_ite.f_player_id];
                            logger.info("Type: ", type, " -> MMM >> suffixes: ", suffixes);
                            if (!!suffixes) {
                                _ite.isOnline = true;
                            } else {
                                _ite.isOnline = false;
                            }
                            usersRes.push(_ite);
                        }
                        break;
                    default:
                        if (_ite.f_player_id != uid) {
                            var suffixes = usersMap[_ite.f_player_id];
                            logger.info("MMM >> suffixes: ", suffixes);
                            if (!!suffixes) {
                                _ite.isOnline = true;
                            } else {
                                _ite.isOnline = false;
                            }
                            usersRes.push(_ite);
                        }
                        break;
                } // end switch
            });

            logger.info("getUsersCacheByType list user online/offline: ", usersRes);
            var resArr = {
                friends: usersRes,
                totalItems: data.totalItems,
                totalPages: data?.totalPages ?? 1,
                currentPage: data?.currentPage ?? 1,
            };

            callback(null, resArr);
        }
    ], function (err, result) {
        cb(null, result);
    });

};


/**
 * Tìm kiếm bạn bè theo tên hoặc uid (không phải user_id)
 * @param data
 * @param cb
 */
FriendService.prototype.searchFriendsHandler = function (data, cb) {
    logger.info("[FriendService.searchFriendsHandler][Step 0] searchFriendsHandler >> data: ", data);
    const userId = data.uid;
    const me = this;
    const type = data.type || 0;
    async.waterfall([
        // function (callback) {
        //     // Step 1: get facebook id from database
        //     // ------------------------------------------------------------------------
        //     userDao.getOpenUserByUid(data.uid, callback);
        // },
        // function(arg1, callback) {
        function(callback) {
            // Step 2: tìm kiếm friend fb của uid theo keyword gửi từ client lên
            // logger.info("step 2: arg1: ", arg1);
            // friendDao.searchFbUserByName(arg1.oid, data.keyword, callback);
            me.app.rpc.db.dbRemote.searchFriends('*', data, (err, code, result) => {
                if (err) {
                    logger.error('1.searchFriendsHandler searchFriends Error:', err);
                    cb(true, err);
                } else {
                    logger.info('1.searchFriendsHandler searchFriends Result:', result);
                    callback(null, result);
                }
            });
        },
        function (data, callback) {
            logger.info("[Step 2] searchFriendsHandler list data friend callback : ", data, " => data.totalItems: ", data.totalItems, ' -> type: ', type);
            // Step 4: get all users online in game
            // ------------------------------------------------------------------------
            if (data.totalItems > 0) {
                logger.info("[Step 2] Có dữ liệu bạn bè online >> thực hiện check user friend online or offline");
                me.__checkAndGetFriendsOnline(userId, type, data, callback);
            }else{
                logger.info("[Step 2] Không có bạn bè nào online >> callback luôn");
                callback(null, data);
            }
        },
        function (data, callback) {
            logger.info("[Step 3] searchFriendsHandler data User Online/Offline: ", data);
            let listUserIds = []
            switch(type) {
                case 1:
                    listUserIds = _.pluck(data.friends, 'player_id');
                    break;
                default:
                    listUserIds = _.pluck(data.friends, 'f_player_id');
                    break;
            }
            
            logger.info("[Step 3] searchFriendsHandler data User Online/Offline >> listUserIds: ", listUserIds);
            // call rpc coin remote get list user_id info
            // me.app.rpc.coin.coinRemote.getAccountInfoByUserIds('*', { userIds: listUserIds }, (err, code, response) => {
            me.app.rpc.authen.authenRemote.getUserInfoByIds('*', { ids: listUserIds }, (err, code, response) => {
                logger.info("[Step 3.1] searchFriendsHandler >> getAccountInfoByUserIds ", response, " >> err: ", err, " >> code: ", code);
                if (!response.success) {
                    logger.error('3.1.searchFriendsHandler Error:', err);
                    cb(true, err);
                } else {
                    logger.info('3.1.searchFriendsHandler Result:', response);
                    callback(null, { 
                        friends: data?.friends, 
                        users: response?.items,
                        totalItems: data.totalItems,
                        totalPages: data?.totalPages ?? 1,
                        currentPage: data?.currentPage ?? 1,
                    });
                }
            });
        }
    ], function (err, result) {
        logger.info("[Step 4] searchFriendsHandler end function show list friend: ", result);
        let _result = [];
        _.each(result.friends, function (item) {
            let minh = {};
            switch(type) {
                case 1:
                    minh = result.users.find(user => user.id === item.player_id);
                    item.balance = item?.player_invite?.balance || 0;
                    item.avatar = item?.player_invite?.avatar ?? '1';
                    break;
                default:
                    minh = result.users.find(user => user.id === item.f_player_id);
                    item.balance = item?.player?.balance || 0;
                    item.avatar = item?.player?.avatar ?? '1';
                    break;
            }

            // const minh = result.users.find(user => user.id === item.f_player_id);
            logger.info("[Step 4.1] searchFriendsHandler end function show list minh: ", minh);
            logger.info("[Step 4.1] searchFriendsHandler end function show list item: ", item);

            item.full_name = minh?.full_name ?? '';
            item.username = minh?.username ?? '';
            item.nick_name = minh?.username ?? '';
            item.is_online = item?.isOnline ?? false;
            item.id = minh.id
            item.uid = minh.user_id
            item.user_id = minh?.user_id ?? '';
            item.gender = minh?.gender || '';

            const reItem = new PlayerLiteDTO(item);
            _result.push(reItem);
        });

        logger.info("[Step 4.1] searchFriendsHandler end function show list _result: ", _result);

        cb(null, {
            friends: _result,
            totalItems: result?.totalItems ?? 0,
            totalPages: result?.totalPages ?? 1,
            currentPage: result?.currentPage ?? 1,
        });
        return;
    });
};


FriendService.prototype.getFriendsByState = function (fbid, state, cb) {
    // TODO
    cb();
};


FriendService.prototype.updateFriends = function (fbid, friends, cb) {
    // TODO
    cb();
};

/**
 * Thêm bạn bè (gửi lời mời kết bạn)
 * @param {number} uid - uid của người dùng gửi yêu cầu kết bạn
 * @param {number} fid - uid của người được mời kết bạn
 * @param {function} cb - callback function
 * @returns {Promise<{code: number, res: any}>} - Promise chứa code và kết quả
 * @returns 
 */
FriendService.prototype.addFriend = async function (uid, fid, cb) {
    logger.info('[friendService.addFriend] with uid: ', uid, ' fid: ', fid);
    const me = this;

    // Step 1: get info of user
    const { codePlayer, resPlayer } = await new Promise((resolve, reject) => {
        me.app.rpc.db.dbRemote.getPlayerById('*', uid, (err, codePlayer, resPlayer) => {
            // logger.info('getPlayerByUserId >> err: ', err, ' >> codePlayer: ', codePlayer, ' >> resPlayer: ', resPlayer);
            if (err) reject(err);
            else resolve({ codePlayer, resPlayer });
        });
    });
    logger.info('[friendService.addFriend] >> codePlayer: ', codePlayer, ' >> resPlayer: ', resPlayer);
    if (codePlayer === CODE.NOT_FOUND) {
        return cb(null, codePlayer, 'user-not-found');
    }

    // Step 2: get info of friend
    const { codeFriend, resFriend } = await new Promise((resolve, reject) => {
        me.app.rpc.db.dbRemote.getPlayerById('*', fid, (err, codeFriend, resFriend) => {
            // logger.info('getPlayerByUserId >> err: ', err, ' >> codeFriend: ', codeFriend, ' >> resFriend: ', resFriend);
            if (err) reject(err);
            else resolve({ codeFriend, resFriend });
        });
    });
    // logger.info('getPlayerByUserId >> codeFriend: ', codeFriend, ' >> resFriend: ', resFriend);
    if (codeFriend === CODE.NOT_FOUND) {
        return cb(null, codeFriend, 'friend-not-found');
    }

    // Step 2.1: Kiểm tra xem đã là bạn bè chưa (type=2)
    const { alreadyFriends, friendExist } = await new Promise((resolve, reject) => {
        const payload = {
            player_id: uid,
            f_player_id: fid,
            type: 2 // type = 2 là đã là bạn bè
        }
        me.app.rpc.db.dbRemote.checkFriendExist('*', payload, (err, code, result) => {
            if (err) reject(err);
            else resolve({ alreadyFriends: code, friendExist: result });
        });
    });
    
    if (friendExist) {
        logger.info('[friendService.addFriend] Users are already friends: %s and %s', uid, fid);
        return cb(null, CODE.FAIL, 'already-friends');
    }
    
    // Step 2.2: Kiểm tra xem đã gửi friend request chưa (type=1)
    const { pendingRequest, requestExist } = await new Promise((resolve, reject) => {
        const payload = {
            player_id: uid, 
            f_player_id: fid,
            type: FRIEND_TYPE.WAITING // type = 1 là đang chờ chấp nhận
        }
        me.app.rpc.db.dbRemote.checkFriendExist('*', payload, (err, code, result) => {
            if (err) reject(err);
            else resolve({ pendingRequest: code, requestExist: result });
        });
    });
    
    if (requestExist) {
        logger.info('[friendService.addFriend] Friend request already sent: %s to %s', uid, fid);
        return cb(null, CODE.FAIL, 'request-already-sent');
    }

    // Step 3: add friend to user and wait accept of friend
    // call rpc database remote to add friend
    const { code, res } = await new Promise((resolve, reject) => {
        const data = {
            uid: uid,
            fid: fid,
            type: FRIEND_TYPE.WAITING, // 0: rejected,  1: waiting, 2: accepted, 3: follow, 4: blocked
            // displayName: '',
        };
        me.app.rpc.db.dbRemote.addFriend('*', data, (err, code, res) => {
            // logger.info('addFriend >> err: ', err, ' >> code: ', code, ' >> res: ', res);
            if (err) reject(err);
            else resolve({ code, res });
        });
    });
    logger.info('[friendService.addFriend] >> code: ', code, ' >> res: ', res);
    
    // Step 4: Gửi thông báo cho người được mời kết bạn nếu họ đang online
    if (code === CODE.OK) {

        const dataPush = {
            from: {
                id: uid,
                user_id: resPlayer?.user_id || 0,
                name: resPlayer?.username || 'Unknown Player',
                avatar: resPlayer?.avatar || '1',
                full_name: resPlayer?.full_name || resPlayer?.username || 'Unknown Player'
            },
            message: `${resPlayer?.full_name || 'Một người chơi'} đã gửi lời mời kết bạn cho bạn.`,
            time: Date.now()
        }

        try {
            // Lấy Redis service từ app
            const redisService = me.app.get('redisService');
            if (!redisService) {
                logger.warn('[friendService.addFriend] Redis service not available');
                // Tiếp tục mà không gửi thông báo
                return cb(null, code, res);
            }
            
            // Kiểm tra trong Redis xem người dùng có online không
            let isOnline = false;
            try {
                isOnline = await redisService.sismember(CONSTS.CACHE.ONLINE_USERS, fid);
                logger.info('[friendService.addFriend] Friend online status: %s, fid: %s', isOnline, fid);
            } catch (redisError) {
                logger.error('[friendService.addFriend] Redis check failed: %s', redisError.message || redisError);
                // Mặc định là offline khi Redis gặp lỗi
                isOnline = false;
            }

            if (isOnline) {
                // Chỉ gửi thông báo khi người chơi online
                const notificationData = {
                    event: CONSTS.GAME.ROUTER.ON_FRIENDS,
                    action: 'friendRequest',
                    data: dataPush
                };
                
                // Gửi thông báo qua messageService
                try {
                    await messageService.pushMessageByUid(fid, CONSTS.GAME.ROUTER.ON_FRIENDS, notificationData, CODE.EVENT.FRIEND_REQUEST);
                    logger.info('[friendService.addFriend] Push notification sent to online user: %s', fid);
                } catch (msgError) {
                    logger.error('[friendService.addFriend] Failed to send notification: %s', msgError.message || msgError);
                }
            } else {
                logger.info('[friendService.addFriend] User not online, skipping push notification: %s', fid);
            }
        } catch (err) {
            // Ghi log lỗi nhưng không ảnh hưởng đến response
            logger.error('[friendService.addFriend] Error in notification process: %s', err.message || err);
        }

        // Step 4.1: Ghi log vào table: logs cho chức năng: email
        logger.info('[friendService.addFriend] Notification sent to user: %s', fid);
        me.app.get('sync').flush('playerSync.addLogs', fid, {
            type: LogTypes.EMAIL,
            board: LogBoards.FRIEND_REQUEST,
            userId: fid,
            senderId: uid,
            amount: 0,
            dataRaw: utils.jsonEndcode(dataPush)
        });

        // Step 4.2: fire emit event to mission add friend
        me.app.event.emit('friendAdded', { playerId: uid, friendId: fid });

    } // end if (code === CODE.OK)

    // Step 5: callback response to client
    return cb(null, code, res);
};

FriendService.prototype.unAddFriend = async function (uid, fid, cb) {
    const me = this;
    // const logger = me.app.get('logger');
    logger.info('[friendService.unAddFriend] uid: %s, fid: %s', uid, fid);
    // Step 1: check friend exist
    const { code, res } = await new Promise((resolve, reject) => {
        me.app.rpc.db.dbRemote.getPlayerById('*', fid, (err, code, res) => {
            // logger.info('getPlayerByUserId >> err: ', err, ' >> code: ', code, ' >> res: ', res);
            if (err) reject(err);
            else resolve({ code, res });
        });
    });
    logger.info('[friendService.unAddFriend] code: %s, res: %s', code, res);
    if (code === CODE.NOT_FOUND) {
        return cb(null, code, 'friend-not-found');
    }

    // Step 2: delete request add friend (type = 1)
    const { code2, res2 } = await new Promise((resolve, reject) => {
        const data = {
            player_id: uid,
            f_player_id: fid,
            type: 1
        };
        me.app.rpc.db.dbRemote.destroyItemFriendWithWhereParams('*', data, (err, code2, res2) => {
            if (err) reject(err);
            else resolve({ code2, res2 });
        });
    });
    logger.info('[friendService.unAddFriend] code2: %s, res2: %s', code2, res2);
    // Step 3: callback response to client
    return cb(null, code2, res2);
};


FriendService.prototype.updateFriendType = async function (uid, fid, type, cb) {

    const me = this;

    // Step 2: get info of friend
    const { codeFriend, resFriend } = await new Promise((resolve, reject) => {
        me.app.rpc.db.dbRemote.getPlayerById('*', fid, (err, codeFriend, resFriend) => {
            // logger.info('getPlayerByUserId >> err: ', err, ' >> codeFriend: ', codeFriend, ' >> resFriend: ', resFriend);
            if (err) reject(err);
            else resolve({ codeFriend, resFriend });
        });
    });
    // logger.info('getPlayerByUserId >> codeFriend: ', codeFriend, ' >> resFriend: ', resFriend);
    if (codeFriend === CODE.NOT_FOUND) {
        return cb(null, codeFriend, 'friend-not-found');
    }

    // Step 3: update type of friend and user to accepted
    // call rpc database remote to add friend
    const { code, res } = await new Promise((resolve, reject) => {
        const data = {
            uid: uid,
            fid: fid,
            type: type, // 0: rejected,  1: waiting, 2: accepted, 3: blocked
        };
        me.app.rpc.db.dbRemote.updateFriendType('*', data, (err, code, res) => {
            // logger.info('addFriend >> err: ', err, ' >> code: ', code, ' >> res: ', res);
            if (err) reject(err);
            else resolve({ code, res });
        });
    });
    // Step 4: callback response to client

    return cb(null, code, res);
};


FriendService.prototype.acceptFriend = async function (uid, fid, type, cb) {
    logger.info('[friendService.acceptFriend] with uid: ', uid, ' fid: ', fid, ' type: ', type);
    const me = this;

    // Step 0: get info of friend
    const { codeFriend, resFriend } = await new Promise((resolve, reject) => {
        me.app.rpc.db.dbRemote.getPlayerById('*', fid, (err, codeFriend, resFriend) => {
            // logger.info('getPlayerByUserId >> err: ', err, ' >> codeFriend: ', codeFriend, ' >> resFriend: ', resFriend);
            if (err) reject(err);
            else resolve({ codeFriend, resFriend });
        });
    });
    // logger.info('getPlayerByUserId >> codeFriend: ', codeFriend, ' >> resFriend: ', resFriend);
    if (codeFriend === CODE.NOT_FOUND) {
        return cb(true, codeFriend, 'friend-not-found');
    }

    // Lấy thông tin người chấp nhận lời mời kết bạn
    const { codeUser, resUser } = await new Promise((resolve, reject) => {
        me.app.rpc.db.dbRemote.getPlayerById('*', uid, (err, codeUser, resUser) => {
            if (err) reject(err);
            else resolve({ codeUser, resUser });
        });
    });
    
    if (codeUser === CODE.NOT_FOUND) {
        return cb(true, codeUser, 'user-not-found');
    }

    // Step 1: kiểm tra xem 2 tài khoản này đã là bạn bè chưa
    const { codeFriend0, resFriend0 } = await new Promise((resolve, reject) => {
        const payload = {
            player_id: uid,
            f_player_id: fid,
            type: 2 // type = 2 nghĩa là đã là bạn bè
        }
        // me.app.rpc.db.dbRemote.checkFriendExist('*', { uid: uid, fid: fid }, (err, codeFriend0, resFriend0) => {
        me.app.rpc.db.dbRemote.checkFriendExist('*', payload, (err, codeFriend0, resFriend0) => {
            // logger.info('checkFriendExist >> err: ', err, ' >> codeFriend: ', codeFriend, ' >> resFriend: ', resFriend);
            if (err) reject(err);
            else resolve({ codeFriend0, resFriend0 });
        });
    })

    logger.info('[friendService.acceptFriend] codeFriend0: ', codeFriend0, ' >> resFriend0: ', resFriend0);
    if (resFriend0) {
        return cb(true, codeFriend0, 'friend-exist');
    }

    // Step 2: update type of friend and user to accepted
    // call rpc database remote to add friend
    const { code, res } = await new Promise((resolve, reject) => {
        const data = {
            uid: fid,
            fid: uid,
            type: type, // 0: rejected,  1: waiting, 2: accepted, 3: blocked
        };
        me.app.rpc.db.dbRemote.updateFriendType('*', data, (err, code, res) => {
            // logger.info('acceptFriend >> err: ', err, ' >> code: ', code, ' >> res: ', res);
            if (err) reject(err);
            // else resolve();
            else resolve({ code, res });
        });
    });

    // logger.info('acceptFriend >> code: ', code, ' >> res: ', res);

    // Step 3: tạo 1 bản ghi mới cho friend và uid
    const { code2, res2 } = await new Promise((resolve, reject) => {
        const data = {
            uid: uid,
            fid: fid,
            type: type, // 0: rejected,  1: waiting, 2: accepted, 3: blocked
            created_at: res?.created_at ?? null,
        };
        me.app.rpc.db.dbRemote.addFriend('*', data, (err, code2, res2) => {
            // logger.info('acceptFriend >> err: ', err, ' >> code: ', code, ' >> res: ', res);
            if (err) reject(err);
            else resolve({ code2, res2 });
        });
    });

    // Step 4: Gửi thông báo cho người đã gửi yêu cầu kết bạn nếu họ đang online
    if (code2 === CODE.OK) {
        const dataPush = {
            from: {
                id: uid,
                user_id: resUser?.user_id || 0,
                name: resUser?.username || 'Unknown Player',
                avatar: resUser?.avatar || '1'
            },
            message: `${resUser?.username || 'Một người chơi'} đã chấp nhận lời mời kết bạn của bạn.`,
            time: Date.now()
        }

        try {
            // Lấy Redis service từ app
            const redisService = me.app.get('redisService');
            if (!redisService) {
                logger.warn('[friendService.acceptFriend] Redis service not available');
                // Tiếp tục mà không gửi thông báo
                return cb(null, code2, res2);
            }
            
            // Kiểm tra trong Redis xem người dùng có online không
            let isOnline = false;
            try {
                isOnline = await redisService.sismember(CONSTS.CACHE.ONLINE_USERS, fid);
                logger.info('[friendService.acceptFriend] Friend online status: %s, fid: %s', isOnline, fid);
            } catch (redisError) {
                logger.error('[friendService.acceptFriend] Redis check failed: %s', redisError.message || redisError);
                // Mặc định là offline khi Redis gặp lỗi
                isOnline = false;
            }

            if (isOnline) {
                // Chỉ gửi thông báo khi người chơi online
                const notificationData = {
                    event: CONSTS.GAME.ROUTER.ON_FRIENDS,
                    action: 'friendAccepted',
                    data: dataPush,
                    // data: {
                    //     from: {
                    //         id: uid,
                    //         user_id: resUser?.user_id || 0,
                    //         name: resUser?.username || 'Unknown Player',
                    //         avatar: resUser?.avatar || '1'
                    //     },
                    //     message: `${resUser?.username || 'Một người chơi'} đã chấp nhận lời mời kết bạn của bạn.`,
                    //     time: Date.now()
                    // }
                };
                
                // Gửi thông báo qua messageService
                try {
                    await messageService.pushMessageByUid(fid, CONSTS.GAME.ROUTER.ON_FRIENDS, notificationData, CODE.EVENT.FRIEND_ACCEPTED);
                    logger.info('[friendService.acceptFriend] Push notification sent to online user: %s', fid);
                } catch (msgError) {
                    logger.error('[friendService.acceptFriend] Failed to send notification: %s', msgError.message || msgError);
                }
            } else {
                logger.info('[friendService.acceptFriend] User not online, skipping push notification: %s', fid);
            }
        } catch (err) {
            // Ghi log lỗi nhưng không ảnh hưởng đến response
            logger.error('[friendService.acceptFriend] Error in notification process: %s', err.message || err);
        }

        // Step 4.1: Ghi log vào table: logs cho chức năng: email
        logger.info('[friendService.acceptFriend] Notification sent to user: %s', fid);
        me.app.get('sync').flush('playerSync.addLogs', fid, {
            type: LogTypes.EMAIL,
            board: LogBoards.FRIEND_ACCEPTED,
            userId: fid,
            senderId: uid,
            amount: 0,
            dataRaw: utils.jsonEndcode(dataPush)
        });

    } // end if (code2 === CODE.OK)

    // Step 5: callback response to client
    return cb(null, code2, res2);
};


FriendService.prototype.rejectFriend = async function (uid, fid, type, cb) {

    const me = this;

    // Step 0: get info of friend
    const { codeFriend, resFriend } = await new Promise((resolve, reject) => {
        me.app.rpc.db.dbRemote.getPlayerById('*', fid, (err, codeFriend, resFriend) => {
            if (err) reject(err);
            else resolve({ codeFriend, resFriend });
        });
    });
    // logger.info('getPlayerByUserId >> codeFriend: ', codeFriend, ' >> resFriend: ', resFriend);
    if (codeFriend === CODE.NOT_FOUND) {
        return cb(true, codeFriend, 'friend-not-found');
    }

    // Step 1: kiểm tra uid theo f_user_id có data không và check xem type có = 1 (waiting) không
    // Có thì mới cho phép reject
    // Không có thì trả về lỗi
    const { codeFriend1, resFriend1 } = await new Promise((resolve, reject) => {
        const payload = {
            player_id: fid,
            f_player_id: uid,
            type: 1 // type = 1 nghĩa là đã là đang chờ chấp nhận
        }
        // me.app.rpc.db.dbRemote.checkFriendExist('*', { uid: uid, fid: fid }, (err, codeFriend0, resFriend0) => {
        me.app.rpc.db.dbRemote.checkFriendExist('*', payload, (err, codeFriend1, resFriend1) => {
            if (err) reject(err);
            else resolve({ codeFriend1, resFriend1 });
        });
    })

    logger.info('acceptFriend >> codeFriend1: ', codeFriend1, ' >> resFriend1: ', resFriend1);
    if (!resFriend1) {
        return cb(true, codeFriend1, 'friend-request-not-found');
    }

    // Step 2: update type of friend and user to rejected
    // call rpc database remote to rejected friend
    const { code, res } = await new Promise((resolve, reject) => {
        const data = {
            uid: fid,
            fid: uid,
            type: type, // 0: rejected,  1: waiting, 2: accepted, 3: blocked
        };
        me.app.rpc.db.dbRemote.updateFriendType('*', data, (err, code, res) => {
            if (err) reject(err);
            else resolve({ code, res });
        });
    });

    // Step 4: callback response to client
    return cb(null, code, res);
};


FriendService.prototype.blockFriend = async function (uid, fid, type, cb) {

    const me = this;

    // Step 0: get info of friend
    const { codeFriend, resFriend } = await new Promise((resolve, reject) => {
        me.app.rpc.db.dbRemote.getPlayerById('*', fid, (err, codeFriend, resFriend) => {
            if (err) reject(err);
            else resolve({ codeFriend, resFriend });
        });
    });

    if (codeFriend === CODE.NOT_FOUND) {
        return cb(true, codeFriend, 'friend-not-found');
    }

    // Step 1: kiểm tra xem 2 tài khoản này có phải bạn bè hay chưa đã
    const { codeFriend0, resFriend0 } = await new Promise((resolve, reject) => {
        const payload = {
            player_id: uid,
            f_player_id: fid,
            type: 2 // type = 2 nghĩa là đã là bạn bè
        }
        me.app.rpc.db.dbRemote.checkFriendExist('*', payload, (err, codeFriend0, resFriend0) => {
            if (err) reject(err);
            else resolve({ codeFriend0, resFriend0 });
        });
    })

    logger.info('acceptFriend >> codeFriend0: ', codeFriend0, ' >> resFriend0: ', resFriend0);
    if (!resFriend0) {
        return cb(true, codeFriend0, 'is-not-friend');
    }

    // Step 2: update type of friend and user to blocked
    // call rpc database remote to blocked friend
    const { code, res } = await new Promise((resolve, reject) => {
        const data = {
            uid: uid,
            fid: fid,
            type: type, // 0: rejected,  1: waiting, 2: accepted, 3: blocked
        };
        me.app.rpc.db.dbRemote.updateFriendType('*', data, (err, code, res) => {
            if (err) reject(err);
            else resolve({ code, res });
        });
    });

    // Step 4: callback response to client
    return cb(null, code, res);
};


FriendService.prototype.unBlockFriend = async function (uid, fid, type, cb) {

    const me = this;

    // Step 0: get info of friend
    const { codeFriend, resFriend } = await new Promise((resolve, reject) => {
        me.app.rpc.db.dbRemote.getPlayerById('*', fid, (err, codeFriend, resFriend) => {
            if (err) reject(err);
            else resolve({ codeFriend, resFriend });
        });
    });

    if (codeFriend === CODE.NOT_FOUND) {
        return cb(true, codeFriend, 'friend-not-found');
    }

    // Step 1: kiểm tra xem 2 tài khoản này có đang chặn nhau hay không
    const { codeFriend0, resFriend0 } = await new Promise((resolve, reject) => {
        const payload = {
            player_id: uid,
            f_player_id: fid,
            type: 3 // type = 3 nghĩa là đã là đang chặn
        }
        me.app.rpc.db.dbRemote.checkFriendExist('*', payload, (err, codeFriend0, resFriend0) => {
            if (err) reject(err);
            else resolve({ codeFriend0, resFriend0 });
        });
    })

    logger.info('acceptFriend >> codeFriend0: ', codeFriend0, ' >> resFriend0: ', resFriend0);
    if (!resFriend0) {
        return cb(true, codeFriend0, 'not-block-friend');
    }

    // Step 2: update type of friend and user to blocked
    // call rpc database remote to blocked friend
    const { code, res } = await new Promise((resolve, reject) => {
        const data = {
            uid: uid,
            fid: fid,
            type: type, // 0: rejected,  1: waiting, 2: accepted, 3: blocked
        };
        me.app.rpc.db.dbRemote.updateFriendType('*', data, (err, code, res) => {
            if (err) reject(err);
            else resolve({ code, res });
        });
    });

    // Step 4: callback response to client
    return cb(null, code, res);
};


FriendService.prototype.unFriend = async function (uid, fid, cb) {

    const me = this;

    // Step 0: get info of friend
    const { codeFriend, resFriend } = await new Promise((resolve, reject) => {
        me.app.rpc.db.dbRemote.getPlayerById('*', fid, (err, codeFriend, resFriend) => {
            if (err) reject(err);
            else resolve({ codeFriend, resFriend });
        });
    });

    if (codeFriend === CODE.NOT_FOUND) {
        return cb(true, codeFriend, 'friend-not-found');
    }

    // Step 1: kiểm tra xem 2 tài khoản có phải bạn bè không
    const { codeFriend0, resFriend0 } = await new Promise((resolve, reject) => {
        const payload = {
            player_id: uid,
            f_player_id: fid,
            type: 2 // type = 2 nghĩa là đang là bạn
        }
        me.app.rpc.db.dbRemote.checkFriendExist('*', payload, (err, codeFriend0, resFriend0) => {
            if (err) reject(err);
            else resolve({ codeFriend0, resFriend0 });
        });
    })

    logger.info('acceptFriend >> codeFriend0: ', codeFriend0, ' >> resFriend0: ', resFriend0);
    if (!resFriend0) {
        return cb(true, codeFriend0, 'is-not-friend');
    }

    // Step 2: delete item friend
    // call rpc database remote to delete friend
    const { code, res } = await new Promise((resolve, reject) => {
        const data = {
            uid: uid,
            fid: fid
        };
        me.app.rpc.db.dbRemote.deleteFriend('*', data, (err, code, res) => {
            if (err) reject(err);
            else resolve({ code, res });
        });
    });

    // Step 4: callback response to client
    return cb(null, code, res);
};


FriendService.prototype.getRandomUsersHandler = function (uid, limit, cb) {
    logger.info('[FriendService.getRandomUsersHandler][Step 0] getRandomUsersHandler with uid: ', uid, ' limit: ', limit);
    const me        = this;
    const pageSize  = limit || CONSTS.EVENT.PAGESIZE;
    let payload     = {};

    async.waterfall([
        // Step 1: lấy ngẫu nhiên danh sách người chơi không phải bạn bè
        function(callback) {
            payload['uid']      = uid; // uuid của user
            payload['limit']    = pageSize;
            me.app.rpc.db.dbRemote.getRandomPlayersNotFriends('*', payload, (err, code, result) => {
                if (err) {
                    logger.error('0. Error:', err);
                    cb(true, err);
                } else {
                    const reResult = {
                        friends: result,
                        totalItems: result?.length ?? 0,
                    }
                    logger.info('0. Result:', result, ' -> reResult: ', reResult);
                    callback(null, reResult);
                }
            });
        },

        // Step 2: get trạng thái online/offline của user
        function (data, callback) {

            logger.info("[Step 1] getRandomUsersHandler list data friend callback : ", data, " => data.totalItems: ", data.totalItems);
            // Step 4: get all users online in game
            // ------------------------------------------------------------------------
            if (data.totalItems > 0) {
                logger.info("Có dữ liệu bạn bè online >> thực hiện check user friend online or offline");
                me.__checkAndGetFriendsOnline(uid, null, data, callback);
            }else{
                logger.info("Không có data >> callback luôn");
                // callback(null, data);
                return cb(null, data);
            }
        },

        // Step 3: get thông tin user từ coin remote
        function (data, callback) {
            logger.info("[Step 2] getRandomUsersHandler data User Online/Offline: ", data);
            const listUserIds = _.pluck(data.friends, 'id');
            logger.info("[Step 2] getRandomUsersHandler data User Online/Offline >> listUserIds: ", listUserIds);
            // call rpc coin remote get list user_id info
            // me.app.rpc.coin.coinRemote.getAccountInfoByUserIds('*', { userIds: listUserIds }, (err, code, response) => {
            me.app.rpc.authen.authenRemote.getUserInfoByIds('*', { ids: listUserIds }, (err, code, response) => {
                logger.info("[Step 2.1] getAccountInfoByUserIds ", response, " >> err: ", err, " >> code: ", code);
                if (!response.success) {
                    logger.error('2.1. Error:', err);
                    cb(true, err);
                } else {
                    logger.info('2.1. Result:', response);
                    callback(null, { players: data?.friends, users: response?.items });
                }
            });
        }
    ], function (err, result) {

        logger.info("[FriendService.getRandomUsersHandler][Step 3] getRandomUsersHandler end function show list friend: ", result);
        let _result = [];

        _.each(result.players, function (item) {
            logger.info("[Step 3.1] getRandomUsersHandler end function show list item: ", item);
            const minh = result.users.find(user => user.id === item.id);
            logger.info("[Step 3.2] getRandomUsersHandler end function show list minh: ", minh);
            // item.balance = item?.balance ?? 0;
            item.full_name = minh?.full_name ?? '';
            item.username = minh?.username ?? '';
            item.is_online = minh?.isOnline ?? false;
            item.type = minh?.type ?? -1;
            item.gender = minh?. gender ?? '';

            const reItem = new PlayerLiteDTO(item);
            _result.push(reItem);
        });

        logger.info("[Step 3.1] getRandomUsersHandler end function show list _result: ", _result);

        cb(null, _result);
        return;
    });
};