'use strict';
const {
  Model
} = require('sequelize');
module.exports = (sequelize, DataTypes) => {
  class logs extends Model {
    /**
     * Helper method for defining associations.
     * This method is not a part of Sequelize lifecycle.
     * The `models/index` file will call this method automatically.
     */
    static associate(models) {
      // define association here
      logs.belongsTo(models.Player, {
        foreignKey: 'sender_id',
        targetKey: 'id', // 'user_id',
        as: 'senderInfo'
      });
      logs.belongsTo(models.Player, {
        foreignKey: 'player_id',
        targetKey: 'id', // 'user_id',
        as: 'playerInfo'
      });
    }
  }
  logs.init({
    id: {
      type: DataTypes.BIGINT.UNSIGNED,
      autoIncrement: true,
      primaryKey: true,
      unique: true,
    },
    type: DataTypes.STRING,
    board: DataTypes.STRING,
    player_id: DataTypes.INTEGER,
    sender_id: DataTypes.INTEGER,
    amount: DataTypes.DECIMAL,
    data_raw: DataTypes.JSON,
    log_timestamp: DataTypes.BIGINT
  }, {
    sequelize,
    modelName: 'Logs',
    tableName: 'logs', 
    underscored: true,
    timestamps: true,
    createdAt: 'created_at',
    updatedAt: 'updated_at'
  });
  return logs;
};