// var pomelo      = require('pomelo');
const logger                    = require('pomelo-logger').getLogger('logs-log', __filename);
const async                     = require('async');
const utils                     = require('../util/utils');
const consts                    = require('../consts/consts');
const logTypes                  = require('../consts/logTypes');
// var mysql       = require('./mysql/mysql');
const { models, Sequelize }     = require("./../models");
const Code                      = require('../consts/code');
const Op                        = Sequelize.Op;
const Logs                      = models.Logs;
const Player                    = models.Player;
const logsDao                   = module.exports;
// var tblName                     = "logs";

/**
 * Hàm check xem người chơi đã nhận thưởng hàng ngày chưa
 * @param {*} payload 
 * @param {*} cb 
 * @returns 
 */
logsDao.isCheckHaveDailyLoginReward = function(payload, cb) {

    const userId    = payload?.userId ?? null;
    const type      = payload?.type ?? '';
    const board     = payload?.board ?? '';
    const startDate = payload?.startDate ?? 0;
    const endDate   = payload?.endDate ?? 0;
    
    if (!userId) {
        return cb(false, Code.BAD_REQUEST, "Content can not be empty!");
    }

    Logs.findOne({
        where: {
            type: type,
            board: board,
            player_id: userId,
            log_timestamp: {
                [Op.gte]: startDate,
                [Op.lte]: endDate
            }
        }
    }).then(data => {
        logger.info("isCheckHaveDailyLoginReward >> data: ", data);
        if (data) {
            // cb(null, Code.OK, data);
            cb(null, null, data);
            return;
        } else {
            // cb(null, Code.NOT_FOUND, null);
            cb(null, null, null);
            return;
        }
    }).catch(err => {
        logger.info("isCheckHaveDailyLoginReward >> err: ", JSON.stringify(err));
        cb(null, Code.FAIL, err);
        return;
    });
}


logsDao.createLog = function(data, cb) {
    logger.info("createLog >> input data: ", data);
    try {
        const currentDatetime = utils.getCurrentDateTime(0, 'YYYY-MM-DD HH24:MI:SS')
        const currentTs       = utils.getCurrentTimestampWithTimezone();
        logger.info("createLog >> currentDatetime: ", utils.convertTimestampToDatetime(currentTs));
        const payload = {
            type: data?.type ?? logTypes.SYSTEM,
            board: data?.board ?? '',
            player_id: data?.userId ?? null,
            sender_id: data?.senderId ?? null,
            amount: data?.amount ?? null,
            log_timestamp: currentTs,
            data_raw: data?.dataRaw ?? null,
            created_at: currentDatetime, // utils.getCurrentDateTime(7, 'YYYY-MM-DD HH24:MI:SS'),
            updated_at: currentDatetime, //utils.getCurrentDateTime(7, 'YYYY-MM-DD HH24:MI:SS'),
            // created_at: new Date().toFormat('YYYY-MM-DD HH24:MI:SS'), // Chuyển đổi định dạng datetime
        }
        logger.info("createLog >> payload: ", payload);
        Logs.create(payload)
            .then(data => {
                // logger.info("createLog >> data: ", data);
                return cb(null, Code.OK, data);
            }).catch(err => {
                logger.info("createLog >> err: ", err.message);
                return cb(null, Code.FAIL, err);
            });

    } catch (error) {
        logger.error("createLog catch >> error: ", error);
        return cb(null, Code.FAIL, error);
    }
}


logsDao.getLogs = async function(payload, cb) {
    logger.info("[logsDao.getLogs] >> payload: ", payload);
    const userId    = payload?.userId ?? null;
    const type      = payload?.type ?? '';
    const dayAgo    = payload?.dayAgo ?? 7;
    // const board     = payload?.board ?? '';
    // const page      = payload?.page ?? 1;
    // const limit     = payload?.limit ?? 10;

    if (!userId) {
        return cb(false, Code.BAD_REQUEST, "Content can not be empty!");
    }

    // const offset    = (page - 1) * limit;

    // Calculate the timestamp for 7 days ago
    const sevenDaysAgo = Math.floor(Date.now() / 1000) - (dayAgo * 24 * 60 * 60);
    logger.info("[logsDao.getLogs] >> sevenDaysAgo: ", sevenDaysAgo);
    try {
        const result = await Logs.findAll({
            where: {
                type: type,
                // board: board,
                player_id: userId,
                log_timestamp: {
                    [Op.gte]: sevenDaysAgo
                }
            },
            // offset: offset,
            // limit: limit,
            include: [{
                model: Player,
                as: 'senderInfo', // bạn phải định nghĩa association với alias là 'player'
                required: false // LEFT JOIN
            }, {
                model: Player,
                as: 'playerInfo', // bạn phải định nghĩa association với alias là 'player'
                required: false // LEFT JOIN
            }],
            order: [
                ['log_timestamp', 'DESC']
            ],
            raw: false // Cần false để có thể truy cập các method của instance
        });

        // const totalPages = Math.ceil(count / limit);
        // const result = {
        //     totalItems: count,
        //     totalPages: totalPages,
        //     currentPage: page,
        //     rows: rows
        // };
        logger.info("[logsDao.getLogs] >> result: ", JSON.stringify(result));
        return cb(null, Code.OK, result);
    } catch (error) {
        logger.error("[logsDao.getLogs] >> error: ", utils.jsonEndcode(error));
        return cb(false, Code.FAIL, error);
    }   
}
