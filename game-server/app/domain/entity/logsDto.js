// const logger     = require('pomelo-logger').getLogger('entry-log', __filename);
var logger          = require('pomelo-logger').getLogger('event-log', __filename);
var LogsDTO = function(opts) {
    this.id             = opts?.id ?? 0;
    this.type           = opts?.type ?? '';
    this.board          = opts?.board ?? 0;
    this.player_id      = opts?.player_id ?? 0;
    this.sender_id      = opts?.sender_id ?? 0;
    this.amount         = opts?.amount ?? 0;
    this.data_raw       = opts?.data_raw ?? '{}';
    this.log_timestamp  = opts?.log_timestamp ?? 0;
    this.created_at     = opts?.created_at ?? 0;
    this.updated_at     = opts?.updated_at ?? 0;
};

var LogsCusDTO = function(opts) {
    // logger.info('LogsCusDTO opts: ', opts?.senderInfo);
    this.id             = parseInt(opts?.id ?? 0);
    this.type           = opts?.type ?? '';
    this.board          = opts?.board ?? 0;
    this.player_id        = opts?.player_id ?? 0;
    // this.sender_id      = opts?.sender_id ?? 0;
    this.amount         = parseInt(opts?.amount ?? 0);
    // this.data_raw       = opts?.data_raw ?? '{}';
    this.log_timestamp  = opts?.log_timestamp ?? 0;
    this.created_at     = opts?.created_at ?? 0;
    // this.updated_at  = opts?.updated_at ?? 0;

    // this.senderInfo     = opts?.senderInfo ?? null;
    if (opts?.senderInfo) {
        // logger.info('LogsCusDTO opts?.senderInfo: ', opts?.senderInfo?.dataValues);
        this.sender_info = {
            // sender_id: opts?.sender_id ?? 0,
            id: opts?.sender_id ?? 0,
            nick_name: opts?.senderInfo?.dataValues?.nick_name ?? '',
            avatar: opts?.senderInfo?.dataValues?.avatar ?? 1,
            display_name: opts?.senderInfo?.dataValues?.display_name ?? '',
        }
    } else {
        this.sender_info = null;
    }

    // define playerInfo
    if (opts?.playerInfo) {
        // logger.info('LogsCusDTO opts?.playerInfo: ', opts?.playerInfo?.dataValues);
        this.player_info = {
            id: opts?.player_id ?? 0,
            nick_name: opts?.playerInfo?.dataValues?.nick_name ?? '',
            avatar: opts?.playerInfo?.dataValues?.avatar ?? 1,
            display_name: opts?.playerInfo?.dataValues?.display_name ?? '',
        }
    } else {
        this.player_info = null;
        // logger.info('LogsCusDTO opts?.playerInfo: ', opts?.playerInfo);
    }

};

/**
 * Expose 'Entity' constructor
 */

// module.exports = LogsDTO;
module.exports = {
    LogsCusDTO,
    LogsDTO
};