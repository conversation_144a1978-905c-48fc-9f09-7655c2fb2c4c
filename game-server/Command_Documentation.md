# TopPoker Server Documentation

## I. Overview

Danh sách các `Command` hỗ trợ giao tiếp giữa `client` và `server` trong game TopPoker.

## II. Commands

---

### 0. Connect vào Gate

Kết nối với IP và Port

- IP: ************ , gate.pokee.club/wss
- Port: 3014

Sau khi kết nối xong thì call command `gate.gateHandler.queryEntry` để lấy thông tin IP và Port của server đích

#### Request

- **CMD**: `gate.gateHandler.queryEntry`
- **Payload Parameters**:
  - **uid**: mặc định 1
- **Example**:
  ```json
  {
    "uid": 1
  }
  ```

#### Response

##### Success

```json
{
  "code": 200,
  "host": "127.0.0.1",
  "port": 3014
}
```

Sau khi nhận được response sẽ có host và port mới thì ngắt kết nối với server gate đi và sử dụng 2 thông tin (host, port) mới này để khởi tạo lại kết nối vào server và bắt đầu login

---

### 1. Đăng nhập bằng deviceId

Đăng nhập người chơi bằng thiết bị, áp dụng cho phiên bản mobile

#### Request

- **CMD**: `connector.entryHandler.loginDevice`
- **Payload Parameters**:
  - **deviceId**: unique device id của người chơi
- **Example**:
  ```json
  {
    "deviceId": "unique_device_id"
  }
  ```

#### Response

##### Success

```json
{
  {
    "code": 200,
    "player": {
      "id": "player_id",
      "username": "player_username",
      "balance": 1000
    },
    "otherBalance": {
      "coinBalance": 11000,
      "diamondBalance": 10000
    }
    "settings": {
      "ios_giftCard": false,
      "ios_giftCode": true,
      "android_giftCard": false,
      "android_giftCode": true
    }
  }
}
```

##### Failure

```json
{
  "code": 500,
  "error": "duplicate-session",
  "message": "duplicate-session"
}
```

---

### 2. Tạo bàn chơi

Tạo bàn chơi.

#### Request

- **CMD**: `game.tableHandler.createTable`
- **Payload Parameters**:
  - **title**(optional): Tên của bàn chơi
  - **smallBlind**: Số tiền cược nhỏ nhất trong trò chơi.
  - **bigBlind**: Số tiền cược lớn nhất trong trò chơi.
  - **minBuyIn**: Số tiền tối thiểu mà người chơi phải mang vào bàn để tham gia trò chơi.
  - **maxBuyIn**: Số tiền tối đa mà người chơi có thể mang vào bàn để tham gia trò chơi.
  - **minPlayers**: Số lượng người chơi tối thiểu cần thiết để bắt đầu trò chơi.
  - **maxPlayers**: Số lượng người chơi tối đa được phép trong trò chơi.
  - **gameMode**: Chế độ của trò chơi, hiện tại có 2 loại là `normal` và `fast`
  - **zone**: Loại sân chơi, hiện tại có 3 loại: `TS, TC, CC`
- **Example**:
  ```json
  {
    "title": "test",
    "smallBlind": 100,
    "bigBlind": 200,
    "minBuyIn": 2000,
    "maxBuyIn": 40000,
    "minPlayers": 2,
    "maxPlayers": 9,
    "gameMode": "normal",
    "zone": "TS"
  }
  ```

#### Response

##### Success

```json
{
  "code": 200,
  "table": {
    "id": "table_id",
    "title": "test",
    "smallBlind": 100,
    "bigBlind": 200,
    "minBuyIn": 2000,
    "maxBuyIn": 40000,
    "minPlayers": 2,
    "maxPlayers": 9,
    "gameMode": "normal",
    "zone": "TS",
    "players": [
      {
        "id": "player_id",
        "username": "player_username",
        "balance": 1000
      }
    ]
  }
}
```

---

### 3. Danh sách bàn chơi

Lấy danh sách các bàn chơi.

#### Request

- **CMD**: `game.tableHandler.getTables`
- **Payload Parameters**:
  - **zone**(optional): Lọc các bàn chơi theo zone. zone đang được định nghĩa làm 3 loại: TS, TC, CC
- **Example**:
  ```json
  {
    "zone": "TS"
  }
  ```

#### Response

##### Success

```json
{
  {
    "code": 200,
    "player": {
      "id": "player_id",
      "username": "player_username",
      "balance": 1000
    },
    "settings": {
      "ios_giftCard": false,
      "ios_giftCode": true,
      "android_giftCard": false,
      "android_giftCode": true
    }
  }
}
```

##### Failure

```json
{
  "code": 500,
  "error": "duplicate-session",
  "message": "duplicate-session"
}
```

---

### 4. Vào 1 bàn chơi

Vào 1 bàn chơi cụ thể nhưng không ngồi xuống mà đứng xem

#### Request

- **CMD**: `game.tableHandler.joinTable`
- **Payload Parameters**:
  - **tid**: uuid của bàn chơi cụ thể
- **Example**:
  ```json
  {
    "tid": "c73883c3-a322-11ef-82f1-9fb710c84ba7"
  }
  ```

---

### 5. Ngồi xuống bàn chơi

Sau khi vào 1 bàn chơi, tiến hành chọn 1 ghếp trống và ngồi xuống để tham gia chơi

#### Request

- **CMD**: `game.tableHandler.joinGame`
- **Payload Parameters**:
  - **buyIn**: số chips người chơi mang xuống bàn để chơi
  - **index**: vị trí ghế ngồi trên bàn
- **Example**:
  ```json
  {
    "buyIn": 10000,
    "index": 1
  }
  ```

#### Response

- **code**: trạng thái mã
  - **200**: thành công
  - **1003**: không đủ tiền
  - **3000**: invalid-table
  - **3002**: table-not-found
  - **3003**: invalid-actorNr
  - **3004**: action-sitdown-is-active
  - **3005**: already-joined
  - **3006**: invalid-buyin
  - **3007**: user-is-playing
  - **3008**: user-not-found
  - **3009**: playing-in-other-table
  - **3010**: below-minimum-buyin

---

### 6. Rời khỏi bàn chơi

Rời khỏi bàn chơi

#### Request

- **CMD**: `game.tableHandler.leaveTable`
- **Payload Parameters**: không cần truyền để rỗng
- **Example**:
  ```json
  {}
  ```

---

### 7. Thoát Game

- Logout, thoát tài khoản

#### Request

- **CMD**: `connector.entryHandler.logout`
- **Payload Parameters**:
  - **uid**: đây là userId dạng uuid của người chơi
- **Example**:
  ```json
  {
    "uid": "102ea6ff-3f76-400f-b2fc-9c7013d6ff9f"
  }
  ```

---

### 8. Đứng dậy

Đang ở chế độ ngồi xuống bàn, commmand này sẽ thực hiện hành động đứng dậy nhưng vẫn ở trong bàn ở chế độ xem

#### Request

- **CMD**: `game.tableHandler.standUp`
- **Payload Parameters**: không cần truyền tham số
- **Example**:
  ```json
  {}
  ```

---

### 9. Lấy thông tin người chơi theo userId

Xem thông tin 1 người chơi theo userId

#### Request

- **CMD**: `game.userHandler.getUserInfo`
- **Payload Parameters**:
  - **uid**: đây là cột uid trong data sau khi login thành công
- **Example**:
  ```json
  {
    "uid": 100253209
  }
  ```

---

### 9.1. Xem thông tin chi tiết của 1 người chơi trong game

Xem thông tin chi tiết của 1 người chơi, ngoài thông tin cá nhân, còn có thông tin bàn chơi mà người đó đang vào (nếu có)

#### Request

- **CMD**: `game.userHandler.viewPlayerInfo`
- **Payload Parameters**:
  - **uid**: uid của người cần xem
- **Example**:
  ```json
  {
    "uid": 6
  }
  ```

#### Response

- player_info: thông tin cá nhân của người được xem (giống data login)
- table_info: thông tin bàn chơi nếu người đó đang chơi (nếu có), không có sẽ trả về null

##### Success

```json
{
  "code": 200,
  "data": {
    "player_info": {
      // đây là thông tin player của người chơi cần xem (giống data login)
    },
    "table_info": {
      "id": "e0d22ac0-37ba-11f0-9f3e-55cfcf4312c5",
      "zone": "TS",
      "smallBlind": 1000,
      "bigBlind": 2000,
      "minBuyIn": 20000,
      "maxBuyIn": 400000,
      "minPlayers": 2,
      "maxPlayers": 5,
      "gameMode": "normal"
    }
  }
}
```

---

### 10. Lấy danh sách users đang đứng ở lobby

Lấy danh sách users đã login vào nhưng chưa tham gia chơi ở bàn nào

#### Request

- **CMD**: `game.tableHandler.getUsersInLobby`
- **Payload Parameters**:
  - **uid**: đây là userId dạng uuid của người chơi
- **Example**:
  ```json
  {
    "uid": "102ea6ff-3f76-400f-b2fc-9c7013d6ff9f"
  }
  ```

---

### 10.1. Đổi/Set mật khẩu

- chức năng cho phép đổi mật khẩu

#### Request

- **CMD**: `game.userHandler.changePassword`
- **Payload Parameters**:
  - **id**: id của player
  - **user_id**: uuid của player
  - **old_password**: mật khẩu cũ
  - **new_password**: mật khẩu mới
- **Example**:
  ```json
  {
    "id": 123,
    "user_id": "100253209",
    "old_password": "123456",
    "new_password": "12345678"
  }
  ```

---

### 11. Đổi bàn chơi

Tự động tìm kiếm 1 bàn tương tự với bàn hiện tại để chuyển sang

#### Request

- **CMD**: `game.tableHandler.switchTable`
- **Payload Parameters**:
  - **tid**: uuid của bàn chơi cụ thể
- **Example**:
  ```json
  {
    "tid": "102ea6ff-3f76-400f-b2fc-9c7013d6ff9f"
  }
  ```

---

### 12. Tìm kiếm bàn chơi

Tìm kiếm bàn chơi theo 1 số tiêu chí nhất định

#### Request

- **CMD**: `game.tableHandler.findTable`
- **Payload Parameters**:
  - **tid**: uuid của bàn chơi cụ thể
  - **gameMode**: Chế độ trò chơi (ví dụ: "normal")
  - **maxPlayers**: Số lượng người chơi tối đa (ví dụ: 9)
  - **minBuyIn**: Số tiền mua vào tối thiểu (ví dụ: 500)
  - **maxBuyIn**: Số tiền mua vào tối đa (ví dụ: 10000)
  - **smallBlind**: Số tiền cược nhỏ (ví dụ: 25)
  - **bigBlind**: Số tiền cược lớn (ví dụ: 50)
  - **zone**: Khu vực (ví dụ: "TS")
- **Example**:
  ```json
  {
    "tid": "102ea6ff-3f76-400f-b2fc-9c7013d6ff9f",
    "gameMode": "normal",
    "maxPlayers": 9,
    "minBuyIn": 500,
    "maxBuyIn": 10000,
    "smallBlind": 25,
    "bigBlind": 50,
    "zone": "TS"
  }
  ```

---

### 13. Gửi nội dung chat

Gửi nội dung chat trong bàn chơi (1 vs n) hoặc giữa người chơi với nhau (1 vs 1)

#### Request

- **CMD**: `chat.chatHandler.sendMessage`
- **Payload Parameters**:
  - **content**: nội dung cần chat
  - **target**: "table" (là chế độ chat trong bàn chơi) or "[uid]" (là chế độ chat riêng 1:1)
- **Example**:
  ```json
  {
    "content": "Hi Toppoker",
    "target": "table"
  }
  ```
  ```json
  {
    "content": "Hi Bro",
    "target": 10
  }
  ```

#### Response

##### Success

```json
{
  "code": 200,
  "route": "chat.chatHandler.sendMessage"
}
```

##### Failure

```json
{
  "code": 500,
  "error": "user-not-exist"
}
```

---

### 14. Lấy danh sách các cuộc trò chuyện

Lấy toàn bộ các cuộc trò chuyện (chat sessions) của người dùng hiện tại

#### Request

- **CMD**: `chat.chatHandler.getChatSessions`
- **Payload Parameters**: không cần truyền tham số
- **Example**:
  ```json
  {}
  ```

#### Response

##### Success

```json
{
  "code": 200,
  "data": [
    {
      "id": 1,
      "sender": {
        "id": 123,
        "nickName": "Player123",
        "avatar": "1"
      },
      "receiver": {
        "id": 456,
        "nickName": "Player456",
        "avatar": "2"
      },
      "createdAt": "2024-12-10T08:30:00Z",
      "lastMessage": {
        "id": 42,
        "senderId": 123,
        "content": "Hello there!",
        "isSystem": false,
        "sentAt": "2024-12-10T10:15:00Z",
        "readAt": null,
        "isRead": false,
        "fileUrl": null,
        "fileType": null
      },
      "unreadCount": 3
    }
  ]
}
```

---

### 15. Lấy lịch sử chat giữa 2 người

Lấy lịch sử tin nhắn chat giữa người dùng hiện tại và một người dùng khác, có hỗ trợ phân trang

#### Request

- **CMD**: `chat.chatHandler.getChatHistories`
- **Payload Parameters**:
  - **target_id**: ID của người dùng đối tác trong cuộc trò chuyện
  - **page**: (tùy chọn) Số trang, mặc định là 1
  - **pageSize**: (tùy chọn) Số lượng tin nhắn mỗi trang, mặc định là 20
- **Example**:
  ```json
  {
    "target_id": 456,
    "page": 1,
    "pageSize": 20
  }
  ```

#### Response

##### Success

```json
{
  "code": 200,
  "data": {
    "count": 42,
    "rows": [
      {
        "id": 42,
        "sessionId": 1,
        "senderId": 123,
        "receiverId": 456,
        "message": "Hello there!",
        "isRead": false,
        "readAt": null,
        "sentAt": "2024-12-10T10:15:00Z",
        "deletedAt": null,
        "isSystem": false,
        "fileUrl": null,
        "fileType": null
      }
    ]
  }
}
```

---

### 16. Đếm số lượng tin nhắn chưa đọc

Đếm số lượng tin nhắn chưa đọc của người dùng hiện tại theo từng phiên chat

#### Request

- **CMD**: `chat.chatHandler.getUnreadMessageCount`
- **Payload Parameters**: không cần truyền tham số
- **Example**:
  ```json
  {}
  ```

#### Response

##### Success

```json
{
  "code": 200,
  "data": [
    {
      "session_id": 1,
      "count": 3
    },
    {
      "session_id": 2,
      "count": 5
    }
  ]
}
```

---

### 17. Đánh dấu tất cả tin nhắn đã đọc

Đánh dấu tất cả tin nhắn của người dùng hiện tại là đã đọc

#### Request

- **CMD**: `chat.chatHandler.markAllMessagesAsRead`
- **Payload Parameters**: không cần truyền tham số
  - **friend_id:** id của đối tượng
- **Example**:
  ```json
  {}
  ```

#### Response

##### Success

```json
{
  "code": 200,
  "data": true
}
```

---

### 18. Xóa tin nhắn

Xóa một tin nhắn cụ thể (chỉ người gửi mới có thể xóa tin nhắn của mình)

#### Request

- **CMD**: `chat.chatHandler.deleteMessage`
- **Payload Parameters**:
  - **message_id**: ID của tin nhắn cần xóa
- **Example**:
  ```json
  {
    "message_id": 42
  }
  ```

#### Response

##### Success

```json
{
  "code": 200,
  "data": true
}
```

---

### 19. Xử lý hành động trong 1 ván chơi

Command xử lý logic liên quan đến 1 ván chơi, Thực hiện các hành động : Fold, Check, Bet, Raise..

#### Request

- **CMD**: `game.tableHandler.execute`
- **Payload Parameters**:
  - **action**: call | bet | check | allin | fold
  - **amt**: số chip tương ứng với các hành động (Bet)
- **Example**:
  ```json
  {
    "action": "bet",
    "amt": "100"
  }
  ```

---

### 20. Đăng nhập bằng facebook

Đăng nhập bằng facebook

#### Request

- **CMD**: `connector.entryHandler.loginFacebook`
- **Payload Parameters**:
  - **accessToken**: access token facebook
  - **userInfo**: object user info from facebook
    - **id**: id facebook
    - **name**: full name lấy từ facebook
    - **email**: địa chỉ email lấy từ facebook
- **Example**:
  ```json
  {
    "accessToken": "...",
    "userInfo": {
      "id": 3158302040978972,
      "name": "Full Name",
      "email": "<EMAIL>"
    }
  }
  ```

---

### 21. Đăng nhập bằng email/password

Đăng nhập bằng email/password

#### Request

- **CMD**: `connector.entryHandler.loginByEmail`
- **Payload Parameters**:
  - **email**: email
  - **password**: mật khẩu
- **Example**:
  ```json
  {
    "email": "<EMAIL>",
    "password": "p@kee123"
  }
  ```

---

### 22. Mời chơi cùng

Chức năng mời bạn bè hoặc người chơi khác đang ở lobby chơi cùng mình
Chú ý: chức năng này từ phía người chơi phải Listener event: `onInvitePlay`

#### Request

- **CMD**: `game.userHandler.sendInvitePlay`
- **Payload Parameters**:
  - **tid**: uuuid của bàn chơi cụ thể
  - **receiver_id**: uuid của người được mời
  - **pos**: vị trí là số thứ tự ngồi trên bàn chơi
- **Example**:
  ```json
  {
    "sender_id": "c05b92b4-ca10-447e-9607-************",
    "tid": "0779f620-abad-11ef-9cd4-f78fa363cc85",
    "receiver_id": "365e107f-5a34-4b8f-b6ec-25e7a8f6e37e",
    "pos": 2
  }
  ```

#### Response

##### Success

```json
{
  "code": 200
}
```

---

### 23.1. Gửi yêu cầu kết bạn

Gửi yêu cầu kết bạn

#### Request

- **CMD**: `game.userHandler.addFriend`
- **Payload Parameters**:
  - **fid**: id của người nhận
- **Example**:
  ```json
  {
    "fid": 123
  }
  ```

---

### 23.2. Hủy yêu cầu: gửi kết bạn

Khi người chơi đã gửi yêu cầu kết bạn (23.1) thì tự động chuyển sang trạng thái đang `follow` bạn bè, chời bạn bè chấp nhận lời mời
Hàm này có mục đích sẽ xóa yêu cầu gửi kết bạn của mình với đối phương.

#### Request

- **CMD**: `game.userHandler.unAddFriend`
- **Payload Parameters**:
  - **fid**: id của người nhận
- **Example**:
  ```json
  {
    "fid": 133
  }
  ```

---

### 24. Chấp nhận yêu cầu kết bạn

Gửi yêu cầu kết bạn

#### Request

- **CMD**: `game.userHandler.acceptFriend`
- **Payload Parameters**:
  - **fid**: id của người nhận
- **Example**:
  ```json
  {
    "fid": 336
  }
  ```

---

### 25. Từ chối yêu cầu kết bạn

Từ chối yêu cầu kết bạn

#### Request

- **CMD**: `game.userHandler.rejectFriend`
- **Payload Parameters**:
  - **fid**: id của người nhận
- **Example**:
  ```json
  {
    "fid": 365
  }
  ```

---

### 26. Chặn bạn bè

Chặn chế độ bạn bè, điều kiện để thực hiện là bạn bè trước đó

#### Request

- **CMD**: `game.userHandler.blockFriend`
- **Payload Parameters**:
  - **fid**: id của người nhận
- **Example**:
  ```json
  {
    "fid": 133
  }
  ```

---

### 27. Bỏ Chặn bạn bè

Bỏ chặn bạn bè, đưa về chế độ bạn bè

#### Request

- **CMD**: `game.userHandler.unBlockFriend`
- **Payload Parameters**:
  - **fid**: uid của người nhận
- **Example**:
  ```json
  {
    "fid": 111
  }
  ```

---

### 28. Hủy chế độ bạn bè

Hủy chế độ bạn bè (người hủy sẽ mất, còn người bị hủy vẫn nhìn thấy là bạn bè)

#### Request

- **CMD**: `game.userHandler.unFriend`
- **Payload Parameters**:
  - **fid**: id của người nhận
- **Example**:
  ```json
  {
    "fid": 11111
  }
  ```

---

### 29. Lấy ngẫu nhiên người chơi

Lấy ngẫu nhiên người chơi để hiển thị vào danh sách cho phép chọn kết bạn

#### Request

- **CMD**: `game.userHandler.getRandomUsers`
- **Payload Parameters**:
  - **limit**: số lượng cần lấy
- **Example**:
  ```json
  {
    "limit": 3
  }
  ```

#### Response

- **user_id**: id người chơi
- **username**: username người chơi
- **avatar**: avatar người chơi
- **balance**: số chips của người chơi
- **is_online**: true || false: trạng thái online

---

### 30. Lấy danh sách bạn bè

Lấy danh sách bạn bè theo 1 số tiêu chí

#### Request

- **CMD**: `game.userHandler.getFriends`
- **Payload Parameters**:
  - **type**: loại cần lấy dữ liệu:
    - 1: danh sách lời mời kết bạn (là người khác gửi lời mới tới mình) (C)
    - 2: đây là danh sách bạn bè đã được chấp nhận (là bạn bè của tôi) (D)
    - 3: Danh sách theo dõi (B) (người đã gửi lời mời kết bạn nhưng chưa phản hồi hoặc đã từ chối)
    - 4: Danh sách đã chặn
  - **page**: trang cần lấy
  - **limit**: số lượng cần lấy
- **Example**:
  ```json
  {
    "type": 0,
    "page": 1,
    "limit": 3
  }
  ```

#### Response

- **friends**:
  - **id**: uuid người chơi
  - **user_id**: uuid người chơi
  - **uid**: uid kiểu số
  - **username**: username người chơi
  - **full_name**: họ tên người chơi
  - **avatar**: avatar người chơi
  - **balance**: số chips của người chơi
  - **is_online**: true || false: trạng thái online
  - **type**: ý nghĩa tương ứng với phần type trên Payload Parameters
- **currentPage**: trang hiện tại
- **totalItems**: tổng số items
- **totalPages**: tổng số trang

---

### 31. Tìm kiếm bạn bè theo từng nhóm

Hỗ trợ tìm kiếm bạn bè theo 1 số trường hợp

#### Request

- **CMD**: `game.userHandler.searchFriends`
- **Payload Parameters**:
  - **type**: loại cần lấy dữ liệu:
    - 0: search all
    - 1: danh sách lời mời kết bạn (là người khác gửi lời mới tới mình) (C)
    - 2: đây là danh sách bạn bè đã được chấp nhận (là bạn bè của tôi) (D)
    - 3: Danh sách theo dõi (B) (người đã gửi lời mời kết bạn nhưng chưa phản hồi hoặc đã từ chối)
    - 4: Danh sách đã chặn
  - **page**: trang cần lấy
  - **limit**: số lượng cần lấy
  - **keyword**: nội dung từ khóa cần tìm kiếm: Tên or Uid
- **Example**:
  ```json
  {
    "type": 2, // danh sách bạn bè
    "page": 1,
    "limit": 3,
    "keyword": ""
  }
  ```

#### Response

- **friends**:
  - **id**: uuid người chơi
  - **user_id**: uuid người chơi
  - **uid**: uid kiểu số
  - **username**: username người chơi
  - **full_name**: họ tên người chơi
  - **avatar**: avatar người chơi
  - **balance**: số chips của người chơi
  - **is_online**: true || false: trạng thái online
  - **type**: trạng thái kết bạn: 1 = đang follow, 2 = đã kết bạn, 3 = đã bị chặn, -1 là mặc định, chưa làm gì của nhau :D
- **currentPage**: trang hiện tại
- **totalItems**: tổng số items
- **totalPages**: tổng số trang

---

### 32. Chơi ngay

không cần chọn bàn chơi, sau khi bấm nút `Chơi ngay`, hệ thống sẽ tự động tìm bàn chơi phù hợp với thông tin bàn chơi tương ứng

- sử dụng `tid` để call cmd: joinTable, và lưu thông tin bàn chơi tương ứng để sử dụng hiển thị trong table (nếu cần)

#### Request

- **CMD**: `game.tableHandler.quickJoinTable`
- **Response Example**:
  ```json
  {
    "msg": {
      "id": "c73883c3-a322-11ef-82f1-9fb710c84ba7",
      .....
    }
    "tid": "c73883c3-a322-11ef-82f1-9fb710c84ba7"
  }
  ```

---

### 33. Lấy danh sách Email và Tin nhắn hệ thống

Trả về danh sách Email và Tin nhắn hệ thống (dữ liệu lấy 7 ngày gần nhất), dựa vào `type` và `board`, `log_timestamp` để tự format text hiển thị theo từng ngôn ngữ tương ứng

- type: `EMAIL`: danh sách email
  - board: `FRIEND_REQUEST` : thông báo hành động có người gửi yêu cầu kết bạn
  - board: `FRIEND_ACCEPTED` : Thông báo hành động có người đã chấp nhận yêu cầu kết bạn
- type: `SYSTEM`: danh sách tin nhắn hệ thống
  - board: `DAILY_LOGIN` : đây là sự kiện nhận thưởng đăng nhập hàng ngày

#### Request

- **CMD**: `game.userHandler.getMessagesAndEmails`

#### Response

- **Example**:
  ```json
  {
    "msg": {
      "emails": [
        {
          "id": 1,
          "user_id": "c73883c3-a322-11ef-82f1-9fb710c84ba7",
          "type": "EMAIL",
          "board": "DAILY_LOGIN",
          "amount": 3000000,
          "log_timestamp": 1733674059,
          "created_at": "2024-12-08 23:07:39",
          "sender_info": null,
          "player_info": {
            "id": 19,
            "nick_name": "dvminh4",
            "avatar": "https://s3.pokee.club/avatar/A44.png",
            "display_name": "Trung Đức Trương"
          }
        }
      ],
      "systems": [
        {
          "id": 2,
          "user_id": "c73883c3-a322-11ef-82f1-9fb710c84ba7",
          "type": "SYSTEM",
          "board": "DAILY_LOGIN",
          "amount": 3000000,
          "log_timestamp": 1733674059,
          "created_at": "2024-12-08 23:07:39",
          "sender_info": {
            "id": 412,
            "nick_name": "admin",
            "display_name": "admin",
            "avatar": 1
          }
        }
      ]
    }
  }
  ```

### 34. Send Notification to a Table

Send a notification to all players at a specific table.

#### Request

- **CMD**: `game.notificationHandler.sendTableNotification`
- **Payload Parameters**:
  - **tid**: UUID of the target table
  - **title**: (optional) Title of the notification
  - **content**: Content of the notification
  - **type**: (optional) Type of notification - 'info', 'warning', 'error', 'success'
  - **senderId**: (optional) ID of the sender
  - **senderName**: (optional) Name of the sender
- **Example**:
  ```json
  {
    "tid": "c73883c3-a322-11ef-82f1-9fb710c84ba7",
    "title": "Table Notification",
    "content": "This table will close for maintenance in 5 minutes",
    "type": "warning",
    "senderId": 1,
    "senderName": "Admin"
  }
  ```

#### Response

##### Success

```json
{
  "code": 200,
  "msg": "Notification sent successfully"
}
```

##### Failure

```json
{
  "code": 3002,
  "msg": "Table not found"
}
```

---

### 35. Send Notification to a Zone

Send a notification to all players in a specific zone.

#### Request

- **CMD**: `game.notificationHandler.sendZoneNotification`
- **Payload Parameters**:
  - **zone**: Zone identifier (TS, TC, CC)
  - **title**: (optional) Title of the notification
  - **content**: Content of the notification
  - **type**: (optional) Type of notification - 'info', 'warning', 'error', 'success'
  - **senderId**: (optional) ID of the sender
  - **senderName**: (optional) Name of the sender
- **Example**:
  ```json
  {
    "zone": "TS",
    "title": "Zone Notification",
    "content": "Tournament starting in 10 minutes in Tournament Zone!",
    "type": "info",
    "senderId": 1,
    "senderName": "Admin"
  }
  ```

#### Response

##### Success

```json
{
  "code": 200,
  "msg": "Zone notification sent successfully"
}
```

##### Failure

```json
{
  "code": 2001,
  "msg": "Invalid zone"
}
```

---

### 35(1). Send Notification to All Zone

Send a notification to all players in all zone.

#### Request

- **CMD**: `game.notificationHandler.sendAllZoneNotification`
- **Payload Parameters**:
  - **title**: (optional) Title of the notification
  - **content**: Content of the notification
  - **type**: (optional) Type of notification - 'info', 'warning', 'error', 'success'
  - **senderId**: (optional) ID of the sender
  - **senderName**: (optional) Name of the sender
- **Example**:
  ```json
  {
    "title": "Zone Notification",
    "content": "Tournament starting in 10 minutes in Tournament Zone!",
    "type": "info",
    "senderId": 1,
    "senderName": "Admin"
  }
  ```

#### Response

##### Success

```json
{
  "code": 200,
  "msg": "[TS, TC, CC] Zone notification sent successfully"
}
```

---

### 36. Send Global Notification

Send a notification to all players connected to the game.

#### Request

- **CMD**: `game.notificationHandler.sendGlobalNotification`
- **Payload Parameters**:
  - **title**: (optional) Title of the notification
  - **content**: Content of the notification
  - **type**: (optional) Type of notification - 'info', 'warning', 'error', 'success'
  - **senderId**: (optional) ID of the sender
  - **senderName**: (optional) Name of the sender
- **Example**:
  ```json
  {
    "title": "System Announcement",
    "content": "Server maintenance scheduled for tomorrow at 3:00 AM UTC",
    "type": "warning",
    "senderId": 1,
    "senderName": "Admin"
  }
  ```

#### Response

##### Success

```json
{
  "code": 200,
  "msg": "Global notification sent successfully"
}
```

##### Failure

```json
{
  "code": 1000,
  "msg": "No permission to send notifications"
}
```

### 37. Send Lobby Notification

Send a notification to all players currently in the lobby (not in any table).

#### Request

- **CMD**: `game.notificationHandler.sendLobbyNotification`
- **Payload Parameters**:
  - **title**: (optional) Title of the notification
  - **content**: Content of the notification
  - **type**: (optional) Type of notification - 'info', 'warning', 'error', 'success'
  - **senderId**: (optional) ID of the sender
  - **senderName**: (optional) Name of the sender
- **Example**:
  ```json
  {
    "title": "Lobby Announcement",
    "content": "New tournament starting in 5 minutes!",
    "type": "info",
    "senderId": 1,
    "senderName": "Admin"
  }
  ```

#### Response

##### Success

```json
{
  "code": 200,
  "msg": "Lobby notification sent successfully"
}
```

---

### 38. Lấy thông tin luật chơi và cấu hình mini game

Lấy thông tin chi tiết về luật chơi, tỷ lệ trả thưởng và các cấu hình khác của mini game quay thưởng.

#### Request

- **CMD**: `game.minigameHandler.getGameInfo`
- **Payload Parameters**: không cần truyền tham số
- **Example**:
  ```json
  {}
  ```

#### Response

- **Example**:
  ```json
  {
    "code": 200,
    "data": {
      "payoutRates": {
        "ROYAL_FLUSH": 10000,
        "STRAIGHT_FLUSH": 1000,
        "FOUR_OF_A_KIND": 200,
        "FULL_HOUSE": 80,
        "FLUSH": 20,
        "STRAIGHT": 10,
        "THREE_OF_A_KIND": 5,
        "TWO_PAIR": 3,
        "ONE_PAIR": 1,
        "HIGH_CARD": 0
      },
      "betOptions": [1000000, 2000000, 4000000],
      "handTypes": {
        "ROYAL_FLUSH": "Sảnh Chúa",
        "STRAIGHT_FLUSH": "Sảnh Thùng",
        "FOUR_OF_A_KIND": "Tứ Quý",
        "FULL_HOUSE": "Cù Lũ",
        "FLUSH": "Đồng Hoa",
        "STRAIGHT": "Sảnh",
        "THREE_OF_A_KIND": "Ba Lá",
        "TWO_PAIR": "Hai Đôi",
        "ONE_PAIR": "Bài Đôi",
        "HIGH_CARD": "Bài Cao"
      }
    }
  }
  ```

---

### 39. Lấy thông tin bài may mắn ngày hôm nay

Lấy thông tin về lá bài may mắn của ngày hôm nay. Lá bài may mắn sẽ nhân đôi hoặc nhân ba phần thưởng khi xuất hiện trong bộ bài quay được.

#### Request

- **CMD**: `game.minigameHandler.getLuckyCard`
- **Payload Parameters**: không cần truyền tham số
- **Example**:
  ```json
  {}
  ```

#### Response

- **Example**:
  ```json
  {
    "code": 200,
    "data": {
      "rank": "K",
      "suit": "H",
      "code": "KH",
      "multiplier": 2,
      "expiresAt": 1733760000000,
      "createdAt": 1733674059000
    }
  }
  ```

---

### 40. Đặt cược và quay thưởng

Đặt cược một số chips và quay để nhận bộ bài ngẫu nhiên. Phần thưởng được tính dựa trên loại bộ bài và sự xuất hiện của bài may mắn.

#### Request

- **CMD**: `game.minigameHandler.spin`
- **Payload Parameters**:
  - **betAmount**: Số chips đặt cược (1000000, 2000000 hoặc 4000000)
- **Example**:
  ```json
  {
    "betAmount": 1000000
  }
  ```

#### Response

- **Example**:
  ```json
  {
    "code": 200,
    "data": {
      "cards": [
        { "rank": "A", "suit": "S", "code": "AS" },
        { "rank": "K", "suit": "S", "code": "KS" },
        { "rank": "Q", "suit": "S", "code": "QS" },
        { "rank": "J", "suit": "S", "code": "JS" },
        { "rank": "T", "suit": "S", "code": "TS" }
      ],
      "handType": "ROYAL_FLUSH",
      "handName": "Sảnh Chúa",
      "betAmount": 1000000,
      "reward": {
        "amount": 10000000000,
        "isLucky": true,
        "luckyMultiplier": 2
      },
      "balance": 12500000000
    }
  }
  ```

---

### 41. Truy vấn lịch sử quay thưởng

Lấy lịch sử quay thưởng của người chơi.

#### Request

- **CMD**: `game.minigameHandler.getSpinHistory`
- **Payload Parameters**:
  - **page**: Số trang (mặc định 1)
  - **pageSize**: Số lượng kết quả mỗi trang (mặc định 10)
- **Example**:
  ```json
  {
    "page": 1,
    "pageSize": 10
  }
  ```

#### Response

- **Example**:
  ```json
  {
    "code": 200,
    "data": {
      "history": [
        {
          "id": "spin_id_1",
          "timestamp": 1733674059000,
          "betAmount": 1000000,
          "handType": "ROYAL_FLUSH",
          "cards": ["AS", "KS", "QS", "JS", "TS"],
          "reward": 10000000000,
          "isLucky": true,
          "multiplier": 2
        },
        {
          "id": "spin_id_2",
          "timestamp": 1733673950000,
          "betAmount": 2000000,
          "handType": "FULL_HOUSE",
          "cards": ["AH", "AD", "AC", "KH", "KD"],
          "reward": 160000000,
          "isLucky": false,
          "multiplier": 1
        }
      ],
      "pagination": {
        "currentPage": 1,
        "totalPages": 5,
        "totalItems": 42
      }
    }
  }
  ```

---

### 42. Lấy danh sách thành tích

Lấy danh sách thành tích của hệ thống và trạng thái hoàn thành của người chơi với mỗi thành tích đó.

#### Request

- **CMD**: `game.userHandler.getAllBadges`
- **Payload Parameters**: nếu lấy tất cả thì không cần truyền tham số
  - **type**: POKE_CAREER (nghề Poker), ACHIEVEMENTS(chiến tích), hoặc cả 1 mảng ['POKE_CAREER', 'ACHIEVEMENTS'], hoặc để null để lấy tất cả, [ 'POKE_CAREER', 'ACHIEVEMENTS' ]
- **Example**:
  ```json
  {
    "type": "POKE_CAREER" // hoặc "ACHIEVEMENTS", [ 'POKE_CAREER', 'ACHIEVEMENTS' ]
  }
  ```

#### Response

- **Example**:

  ```json
  {
    "code": 200,
    "route": "game.userHandler.getAllBadges",
    "msg": {
      "badges": [
        {
          "id": 1,
          "badge_id": 1,
          "type": "POKE_CAREER",
          "name": "Nâng cấp lần thứ 1",
          "code": "LEVEL_UP_2",
          "description": "Lên đến cấp 2",
          "reward": null,
          "category": "LEVEL",
          "icon_url": null,
          "created_at": "2025-04-25 00:24:29",
          "claimed_at": "null", // ngày nhận thưởng
          "awarded_at": "2025-04-25 00:24:29", // ngày đạt được thành tích
          "player_reward": {
            "id": 123,
            "player_id": 456,
            "badge_id": 1,
            "completion_percent": 16, // số % hoàn thành tiến độ
            "is_completed": 0, // 0: chưa hoàn thành, 1: đã hoàn thành
            "awarded_at": "2025-04-25 00:24:29", // ngày đạt được thành tích
            "created_at": "2025-04-25 00:24:29",
            "claimed_at": "null" // ngày nhận thưởng (nếu đã nhấn), null là chưa nhận giải thưởng
          },
          "rewards": [
            {
              "id": 1,
              "badge_id": 1,
              "reward_type": "CHIPS",
              "reward_value": "1000",
              "is_permanent": false // true nếu là thưởng vĩnh viễn, false nếu là thưởng tạm thời
            },
            {
              "id": 2,
              "badge_id": 1,
              "reward_type": "AVATAR",
              "reward_value": "special_avatar_1",
              "is_permanent": true // true nếu là thưởng vĩnh viễn, false nếu là thưởng tạm thời
            }
          ]
        },
        {
          "id": 2,
          "badge_id": 2,
          "type": "ACHIEVEMENTS",
          "name": "Chiến thắng đầu tiên",
          "code": "FIRST_WIN",
          "description": "Chiến thắng ván đầu tiên",
          "reward": "CHIPS:1000",
          "category": "FIRST_TIME",
          "icon_url": null,
          "created_at": "2023-01-01T00:00:00.000Z",
          "player_reward": null
        }
        // More badges...
      ]
    }
  }
  ```

  Trong đó:

  - `player_reward`: Thông tin về thành tích của người chơi
    - Nếu người chơi đã đạt được thành tích, trường này sẽ chứa thông tin chi tiết
    - Nếu người chơi chưa đạt được thành tích, trường này sẽ là `null`
  - `rewards`: Danh sách các phần thưởng chi tiết của thành tích
    ```json
    "rewards": [
      {
        "id": 1,
        "badge_id": 1,
        "reward_type": "CHIPS",
        "reward_value": "1000",
        "is_permanent": false
      },
      {
        "id": 2,
        "badge_id": 1,
        "reward_type": "AVATAR",
        "reward_value": "special_avatar_1",
        "is_permanent": true
      }
    ]
    ```

---

### 43. Nhận thưởng thành tích

Nhận thưởng thành tích của người chơi. Phần thưởng được cấu hình trong bảng `badge_rewards`.

#### Luồng xử lý phần thưởng

1. **Kiểm tra điều kiện nhận thưởng**:

   - Kiểm tra xem người chơi đã đạt được thành tích chưa
   - Kiểm tra xem người chơi đã nhận thưởng cho thành tích này chưa

2. **Xử lý từng loại phần thưởng**:

   - **CHIPS**:

     - Lấy số dư hiện tại của người chơi
     - Cộng giá trị phần thưởng vào số dư
     - Cập nhật số dư mới
     - Ghi log với thông tin số dư trước và sau khi nhận thưởng

   - **EXP**:

     - Lấy giá trị EXP hiện tại của người chơi
     - Cộng giá trị phần thưởng vào EXP hiện tại
     - Cập nhật giá trị EXP mới
     - Ghi log với thông tin EXP trước và sau khi nhận thưởng

   - **VIP**:

     - Lấy điểm VIP hiện tại của người chơi
     - Cộng giá trị phần thưởng vào điểm VIP hiện tại
     - Cập nhật giá trị điểm VIP mới
     - Ghi log với thông tin điểm VIP trước và sau khi nhận thưởng

   - **AVATAR/TITLE/DECORATION**:
     - Thêm vật phẩm vào tài khoản người chơi
     - Ghi log với thông tin vật phẩm đã thêm

3. **Gửi thông báo**:
   - Sau khi xử lý phần thưởng, hệ thống sẽ gửi thông báo qua channel `onUpdateMyself`
   - Thông báo bao gồm thông tin chi tiết về phần thưởng và giá trị hiện tại sau khi nhận thưởng

#### Request

- **CMD**: `game.userHandler.claimBadgeReward`
- **Payload Parameters**:
  - **badge_id**: ID của thành tích
- **Example**:
  ```json
  {
    "badge_id": 1
  }
  ```

#### Response

- **Example**:
  ```json
  {
    "code": 200,
    "route": "game.userHandler.claimBadgeReward",
    "msg": {
      "badge": {
        "id": 1,
        "badge_id": 1,
        "player_id": 123,
        "type": "POKE_CAREER",
        "name": "Nâng cấp lần thứ 1",
        "code": "LEVEL_UP_2",
        "description": "Lên đến cấp 2",
        "category": "LEVEL",
        "icon_url": null,
        "awarded_at": "2023-05-15T10:30:00.000Z"
      },
      "reward": {
        "type": "CHIPS",
        "value": 1000
      },
      "claimed_at": "2023-05-16T10:30:00.000Z",
      "rewards": [
        {
          "type": "CHIPS",
          "value": 1000,
          "is_permanent": false,
          "success": true
        },
        {
          "type": "AVATAR",
          "value": "special_avatar_1",
          "is_permanent": true,
          "success": true
        }
      ]
    }
  }
  ```

Trong đó:

- `badge`: Thông tin về thành tích đã nhận thưởng
- `reward`: Phần thưởng chính (ưu tiên CHIPS, sau đó là EXP, sau đó là VIP)
- `claimed_at`: Thời gian nhận thưởng
- `rewards`: Danh sách tất cả các phần thưởng đã nhận thành công
- `failed_rewards`: (nếu có) Danh sách các phần thưởng không thể xử lý

Sau khi nhận thưởng thành công, hệ thống sẽ gửi thông báo qua channel `onUpdateMyself` cho người chơi với các thông tin sau:

```json
// Ví dụ thông báo nhận thưởng CHIPS
{
  "msg": {
    "type": 1009,
    "description": "Bạn nhận được phần thưởng 1,000 chips từ thành tích: Nâng cấp lần thứ 1",
    "user_id": "123",
    "amount": 1000,
    "timestamp": 1733420907,
    "badge_id": 1,
    "badge_name": "Nâng cấp lần thứ 1",
    "balance": 5000000, // Số dư hiện tại sau khi nhận thưởng
    "reward_type": "CHIPS",
    "reward_value": 1000
  },
  "type": "onUpdateMyself",
  "code": 1009
}

// Ví dụ thông báo nhận thưởng EXP
{
  "msg": {
    "type": 1010,
    "description": "Bạn nhận được phần thưởng 50 EXP từ thành tích: Nâng cấp lần thứ 1",
    "user_id": "123",
    "amount": 50,
    "timestamp": 1733420907,
    "badge_id": 1,
    "badge_name": "Nâng cấp lần thứ 1",
    "exp": 150, // EXP hiện tại sau khi nhận thưởng
    "level": 2, // Level hiện tại
    "reward_type": "EXP",
    "reward_value": 50
  },
  "type": "onUpdateMyself",
  "code": 1010
}

// Ví dụ thông báo nhận thưởng VIP
{
  "msg": {
    "type": 1011,
    "description": "Bạn nhận được phần thưởng 100 điểm VIP từ thành tích: Thành tích VIP",
    "user_id": "123",
    "amount": 100,
    "timestamp": 1733420907,
    "badge_id": 5,
    "badge_name": "Thành tích VIP",
    "vippoint": 250, // Điểm VIP hiện tại sau khi nhận thưởng
    "reward_type": "VIP",
    "reward_value": 100
  },
  "type": "onUpdateMyself",
  "code": 1011
}

// Ví dụ thông báo nhận thưởng vật phẩm
{
  "msg": {
    "type": 1012,
    "description": "Bạn nhận được phần thưởng avatar: special_avatar_1 từ thành tích: Nâng cấp lần thứ 1",
    "user_id": "123",
    "item_type": "AVATAR",
    "item_value": "special_avatar_1",
    "is_permanent": true,
    "timestamp": 1733420907,
    "badge_id": 1,
    "badge_name": "Nâng cấp lần thứ 1",
    "reward_type": "AVATAR",
    "reward_value": "special_avatar_1"
  },
  "type": "onUpdateMyself",
  "code": 1012
}
```

#### Xử lý lỗi

- **Thành tích không tồn tại**: Trả về mã lỗi và thông báo
- **Người chơi chưa đạt được thành tích**: Trả về mã lỗi và thông báo
- **Người chơi đã nhận thưởng cho thành tích này**: Trả về mã lỗi và thông báo
- **Lỗi khi xử lý phần thưởng**: Ghi log chi tiết và trả về thông tin lỗi

#### Mã code

- **7000**: thành tích không tồn tại
- **7001**: Badge already claimed
- **7002**: Invalid badge ID
- **7003**: Invalid player ID
- **7004**: Badge đã được nhận trước đó
- **7005**: Chưa đủ điều kiện nhận thưởng

### 44. Lấy danh sách bảng điểm kinh nghiêm

Lấy danh sách tất cả các cấp độ

#### Request

- **CMD**: `game.expHandler.getAllLevelRanks`
- **Payload Parameters**: không gửi gì lên

#### Response

- **Example**:

  ```json
  {
    "code": 200,
    "route": "game.expHandler.getAllLevelRanks",
    "msg": [
      {
        "level": 1,
        "title": "Vào cửa Poker",
        "title_en": "Poker Entry",
        "key": "LEVEL_1",
        "exp_required": 0,
        "total_exp": 0,
        "reward": 0,
        "rank": "Sơ cấp"
      },
      {
        "level": 2,
        "title": "Người mới Poker",
        "title_en": "Poker Newbie",
        "key": "LEVEL_2",
        "exp_required": 10,
        "total_exp": 10,
        "reward": 0,
        "rank": "Sơ cấp"
      },
      ...
    ]
  }
  ```

---

### 45. Lấy danh sách vật phẩm trong shop

Lấy danh sách các vật phẩm có sẵn trong shop.

#### Request

- **CMD**: `game.shopHandler.getShopItems`
- **Payload Parameters**:
  - **type**: (optional) Loại vật phẩm (CHIP, COIN, DIAMOND, PROPS, CARD, DECOR)
  - **category**: (optional) Nhóm vật phẩm con (PT, NICE_NUMBER, SPECIFICITY, BUBBLE_CHAT, DECOR, ENTERTAINMENT, OTHER, HOLIDAY)
  - **currency**: (optional) Loại tiền (CHIP, COIN, DIAMOND, PROPS, CARD, DECOR, USD, VND)
  - **grouped**: (optional) Trả về dạng nhóm theo type và order_index (mặc định: true)
- **Example**:
  ```json
  {
    "type": "CHIP",
    "currency": "DIAMOND",
    "grouped": true
  }
  ```

#### Response

- **Example (grouped=true)**:

  ```json
  {
    "code": 200,
    "data": {
      "CHIP": [
        {
          "order_index": 1,
          "items": [
            {
              "id": 1,
              "type": "CHIP",
              "category": null,
              "item_name": "2,144,000,000 Chip",
              "item_value": 2144000000,
              "price": 50,
              "currency": "DIAMOND",
              "description": "1 Kim cương = 42.88M Chip",
              "item_limit": -1,
              "available_time": -1,
              "gift_status": 0,
              "status": 1,
              "is_sale": 0,
              "note": null,
              "order_index": 1
            }
          ]
        },
        {
          "order_index": 2,
          "items": [
            {
              "id": 2,
              "type": "CHIP",
              "category": null,
              "item_name": "8,754,000,000 Chip",
              "item_value": 8754000000,
              "price": 100,
              "currency": "DIAMOND",
              "description": "1 Kim cương = 87.54M Chip",
              "item_limit": -1,
              "available_time": -1,
              "gift_status": 0,
              "status": 1,
              "is_sale": 0,
              "note": null,
              "order_index": 2
            }
          ]
        }
      ]
    }
  }
  ```

- **Example (grouped=false)**:
  ```json
  {
    "code": 200,
    "data": [
      {
        "id": 1,
        "type": "Chip",
        "category": null,
        "item_name": "2,144,000,000 Chip",
        "item_value": 2144000000,
        "price": 50,
        "currency": "DIAMOND",
        "description": "1 Kim cương = 42.88M Chip",
        "item_limit": -1,
        "available_time": -1,
        "gift_status": 0,
        "status": 1,
        "is_sale": 0,
        "note": null,
        "order_index": 1
      },
      {
        "id": 2,
        "type": "Chip",
        "category": null,
        "item_name": "8,754,000,000 Chip",
        "item_value": 8754000000,
        "price": 100,
        "currency": "DIAMOND",
        "description": "1 Kim cương = 87.54M Chip",
        "item_limit": -1,
        "available_time": -1,
        "gift_status": 0,
        "status": 1,
        "is_sale": 0,
        "note": null,
        "order_index": 2
      }
    ]
  }
  ```

---

### 46. Lấy thông tin chi tiết vật phẩm trong shop

Lấy thông tin chi tiết của một vật phẩm trong shop.

#### Request

- **CMD**: `game.shopHandler.getShopItemDetail`
- **Payload Parameters**:
  - **item_id**: ID của vật phẩm
- **Example**:
  ```json
  {
    "item_id": 1
  }
  ```

#### Response

- **Example**:
  ```json
  {
    "code": 200,
    "data": {
      "id": 1,
      "type": "Chip",
      "category": null,
      "item_name": "2,144,000,000 Chip",
      "item_value": 2144000000,
      "price": 50,
      "currency": "DIAMOND",
      "description": "1 Kim cương = 42.88M Chip",
      "item_limit": -1,
      "available_time": -1,
      "gift_status": 0,
      "status": 1,
      "is_sale": 0,
      "note": null,
      "order_index": 1
    }
  }
  ```

---

### 47. Mua vật phẩm trong shop

Mua một vật phẩm trong shop.

#### Request

- **CMD**: `game.shopHandler.buyItem`
- **Payload Parameters**:
  - **item_id**: ID của vật phẩm
  - **platform**: (optional) Nền tảng mua (iOS, Android, Web)
- **Example**:
  ```json
  {
    "item_id": 1,
    "platform": "iOS"
  }
  ```

#### Response

- **Example**:

  ```json
  {
    "code": 200,
    "data": {
      "accBalances": { "balance": 519801300, "coin": 10000, "diamond": 2900 },
      "item": {
        "id": 1,
        "type": "CHIP",
        "category": null,
        "item_name": "214,400,000 Chip",
        "item_value": 214400000,
        "item_id": 0,
        "price": 50,
        "currency": "DIAMOND",
        "description": "1 Kim cương = 42.88M Chip",
        "item_limit": -1,
        "available_time": -1,
        "icon": "https://s3.pokee.club/shops/chip_0.png",
        "gift_status": 0,
        "status": 1,
        "is_sale": 0,
        "note": null,
        "order_index": 1
      }
    }
  }
  ```

- **pushMessage to event**: `onUpdateMyself`
  ```json
  {
    "type": "onUpdateMyself",
    "code": 1014,
    "msg": {
      "item": {
        "id": 1,
        "type": "CHIP",
        "category": null,
        "item_name": "214,400,000 Chip",
        "item_value": 214400000,
        "item_id": 0,
        "price": 50,
        "currency": "DIAMOND",
        "description": "1 Kim cương = 42.88M Chip",
        "item_limit": -1,
        "available_time": -1,
        "icon": "https://s3.pokee.club/shops/chip_0.png",
        "gift_status": 0,
        "status": 1,
        "is_sale": 0,
        "note": null,
        "order_index": 1
      },
      "accBalances": {
        "balance": 519801300,
        "coin": 1000000,
        "diamond": 950
      }
    }
  }
  ```

---

### 48. Lấy lịch sử mua vật phẩm

Lấy lịch sử mua vật phẩm của người chơi.

#### Request

- **CMD**: `game.shopHandler.getPurchaseHistory`
- **Payload Parameters**:
  - **shop_item_id**: (optional) ID của vật phẩm
  - **type**: (optional) Loại vật phẩm (CHIP, COIN, DIAMOND, PROPS, CARD, DECOR)
  - **category**: (optional) Nhóm vật phẩm con
  - **order_status**: (optional) Trạng thái giao dịch (SUCCESS, FAILED)
  - **limit**: (optional) Số lượng bản ghi tối đa (mặc định: 50)
  - **offset**: (optional) Vị trí bắt đầu (mặc định: 0)
- **Example**:
  ```json
  {
    "type": "CHIP",
    "limit": 10,
    "offset": 0
  }
  ```

#### Response

- **Example**:
  ```json
  {
    "code": 200,
    "data": [
      {
        "id": 1,
        "player_id": "100253171",
        "shop_item_id": 1,
        "item_name": "2,144,000,000 Chip",
        "type": "CHIP",
        "category": null,
        "item_value": 2144000000,
        "price": 50,
        "currency": "DIAMOND",
        "quantity": 1,
        "item_limit": -1,
        "available_time": -1,
        "purchased_at": "2024-07-11T10:15:30.000Z",
        "order_status": "SUCCESS",
        "platform": "iOS",
        "error_message": null
      }
    ],
    "total": 1,
    "limit": 10,
    "offset": 0
  }
  ```

---

### 49. Lấy danh sách nhiệm vụ

Lấy danh sách nhiệm vụ của người chơi hiện tại.

#### Request

- **CMD**: `game.missionsHandler.getMissions`
- **Payload Parameters**: không gửi gì lên
- **Example**:
  ```json
  {}
  ```

#### Response

- **Example**:
  ```json
  {
    "code": 200,
    "data": [
      {
        "id": 1,
        "code": "WIN_5_GAMES",
        "name": "Win 5 Games",
        "description": "Win 5 games in a row",
        "type": "DAILY",
        "condition_json": {
          "win_count": 5
        },
        "reward_json": {
          "coins": 1000
        },
        "repeatable": 0,
        "is_active": 1,
        "start_time": null,
        "end_time": null,
        "player_missions": {
          "completion_percent": 0, // số % hoàn thành nhiệm vụ (0 - 100)
          "is_completed": 0,
          "claimed_at": null,
          "completed_at": null,
          "reward_claimed": 0
        }
      }
      // More missions...
    ]
  }
  ```

---

### 50. Nhận thưởng nhiệm vụ

Nhận thưởng cho nhiệm vụ đã hoàn thành.

#### Request

- **CMD**: `game.missionsHandler.claimReward`
- **Payload Parameters**:
  - **mission_id**: ID của nhiệm vụ
- **Example**:
  ```json
  {
    "mission_id": 1
  }
  ```

#### Response

- **Example**:
  ```json
  {
    "code": 200,
    "data": {
      "mission_id": 1,
      "mission_info": {
        "id": 1,
        "code": "WIN_5_GAMES",
        "name": "Win 5 Games",
        "description": "Win 5 games in a row",
        "type": "DAILY",
        "condition_json": {
          "win_count": 5
        },
        "reward_json": {
          "coins": 1000
        },
        "repeatable": 0,
        "is_active": 1,
        "start_time": null,
        "end_time": null
      },
      "player_id": 123,
      "reward": {
        "coins": 1000
      },
      "claimed_at": "2023-01-01T00:00:00.000Z"
    }
  }
  ```

---

### 51. Get Leaderboards (danh sách bảng xếp hạng)

Lấy danh sách xếp hạng theo loại và phạm vi.

#### Request

- **CMD**: `game.leaderboardHandler.getLeaderboards`
- **Payload Parameters**:
  - **type**: Loại xếp hạng (CHIPS, LEVEL, BADGES), bắt buộc phải gửi
  - **scope**: Phạm vi xếp hạng (GLOBAL, FRIENDS), mặc định không cần gửi
  - **season**: Mùa (tùy chọn, mặc định là tháng hiện tại), mặc định không cần gửi
  - **limit**: Số lượng kết quả trả về (mặc định: 30)
- **Example**:
  ```json
  {
    "type": "CHIPS"
  }
  ```

#### Response

- **Example**:
  ```json
  {
    "code": 200,
    "data": {
      "GLOBAL": [
        {
          "player_id": 1,
          "display_name": "Player 1",
          "avatar": "avatar_url",
          "rank": 1,
          "previous_rank": null,
          "rank_change_status": null,
          "value": 1000
        },
        {
          "player_id": 2,
          "display_name": "Player 2",
          "avatar": "avatar_url",
          "rank": 2,
          "previous_rank": null,
          "rank_change_status": null,
          "value": 900
        }
      ],
      "FRIENDS": [
        //...
      ]
    }
  }
  ```

### 52. Get Player Ranking

Lấy xếp hạng của người chơi theo loại và phạm vi.

#### Request

- **CMD**: `game.leaderboardHandler.getPlayerRanking`
- **Payload Parameters**:
  - **type**: Loại xếp hạng (CHIPS, LEVEL, BADGES), bắt buộc phải gửi
  - **scope**: Phạm vi xếp hạng (GLOBAL, FRIENDS) mặc định giá trị này không cần gửi
  - **season**: Mùa (tùy chọn, mặc định là tháng hiện tại), mặc định giá trị này không cần gửi lên
- **Example**:
  ```json
  {
    "type": "CHIPS"
  }
  ```

#### Response

- **Example**:
  ```json
  {
    "code": 200,
    "data": {
      "GLOBAL": {
        "code": 200,
        "data": {
          "player_id": 1,
          "display_name": "Player 1",
          "avatar": "avatar_url",
          "rank": 1,
          "previous_rank": 2,
          "rank_change_status": "SAME", // 3 giá trị: UP, DOWN, SAME
          "value": 1000,
          "type": "CHIPS",
          "scope": "GLOBAL",
          "season": "2023-01"
        }
      },
      "FRIENDS": {
        "code": 404,
        "data": "Player ranking not found"
      }
    }
  }
  ```

---

### 53. Lấy danh sách đạo cụ của tôi

Lấy danh sách đạo cụ của người chơi.

#### Request

- **CMD**: `game.shopHandler.getMyItems`
- **Payload Parameters**:
  - **type**: PROPS(đạo cụ), GIFT (quà), bắt buộc phải gửi lên (mặc định là PROPS)
  - **limit**: Số lượng bản ghi tối đa (mặc định: 50), mặc định giá trị này không cần gửi lên
  - **offset**: Vị trí bắt đầu (mặc định: 0), mặc định giá trị này không cần gửi lên
- **Example**:
  ```json
  {
    "type": "PROPS",
    "limit": 10,
    "offset": 0
  }
  ```

#### Response

- **Example**:
  ```json
  {
    "code": 200,
    "data": [
      {
        "id":5,
        "playerId":5,
        "itemId":2,
        "quantity":5,
        "acquiredAt":"2025-05-11 01:09:18",
        "expiresAt":null,
        "used":false,
        "item":{
          "id":2,
          "name":"Loa nhỏ",
          "description":"Dùng loa nhỏ phát thanh, tất cả phòng đều có thể thấy được",
          "type":"PROPS",
          "price":0,
          "isConsumable":false,
          "metadata":{
          "effect":"kick_player"
        },
        "effect":"kick_player",
        "duration":null,
        "rarity":"common",
        "isRandomBox":false
      },
      ...
    ]
  }
  ```

---

### 54. Lấy danh sách avatar mặc định

> Có thể sử dụng file static json sau:

- https://s3.pokee.club/config/data/avatars.json

> File Icon ở màn hình login

- https://s3.pokee.club/config/data/avatars_icon.json

- **CMD**: `game.userHandler.getDefaultAvatars`
- **Payload Parameters**: Không cần gửi payload
- **Example**:
  ```json
  {}
  ```

#### Response

- **Example**:
  ```json
  {
    "code": 200,
    "data": {
      "avatars": [
        {
          "id": 1,
          "link_url": "https://s3.pokee.club/avatar/A1.png"
        },
        {
          "id": 2,
          "link_url": "https://s3.pokee.club/avatar/A2.png"
        }
      ]
    }
  }
  ```

---

### 55. Cập nhật thông tin cá nhân

Cập nhật thông tin cá nhân của người chơi như avatar, displayName, hoặc các thông tin khác.

#### Request

- **CMD**: `game.userHandler.updateProfile`
- **Payload Parameters**:
  - **avatar**: (optional) ID của avatar mới
  - **displayName**: (optional) Tên hiển thị của người chơi
  - **gender**: (optional) Giới tính người chơi (MALE, FEMALE, OTHER)
  - **phone**: (optional) số điện thoại của người chơi
- **Example**:
  ```json
  {
    "avatar": "https://s3.pokee.club/avatar/A1.png",
    "displayName": "Harry Pham",
    "gender": "MALE",
    "phone": "0988888888"
  }
  ```

#### Response

##### Success

```json
{
  "code": 200,
  "data": {
    "player": {
      "id": "player_id",
      "user_id": 100253171,
      "uid": 100253171,
      "nick_name": "PokerMaster",
      "username": "player_username",
      "full_name": "Player Full Name",
      "avatar": "https://s3.pokee.club/avatar/A1.png",
      "gender": "MALE",
      "last_login": 1745857621,
      "win": 17,
      "win_rate": "40.4761904",
      "lose": 0,
      "total": 42,
      "properties": {
        "id": 5,
        "day_play_num": 0,
        "day_win_num": 0,
        "day_lost_num": 0,
        "spin_num": 1,
        "last_time_spin": 1738776308,
        "continuous_login_num": 1,
        "continuous_spin_num": 0,
        "is_first_pay": false,
        "play_json": "{\"biggestWon\":3000,\"bestHand\":[\"KH\",\"TH\"],\"codeWin\":600}",
        "small_speaker": 5,
        "big_speaker": 0
      }
    }
  }
}
```

##### Failure

```json
{
  "code": 400,
  "error": "invalid-nickname",
  "message": "Nickname contains invalid characters"
}
```

- **Các mã lỗi phổ biến**:
  - **400**: Dữ liệu không hợp lệ
  - **403**: Không có quyền cập nhật
  - **429**: Vượt quá số lần cập nhật cho phép

---

### 56. Thiết lập mật khẩu két sắt

Thiết lập mật khẩu két sắt cho người chơi.

#### Request

- **CMD**: `game.safeboxHandler.setupPassword`
- **Payload Parameters**:
  - **password**: Mật khẩu két sắt
- **Example**:
  ```json
  {
    "password": "123456"
  }
  ```

#### Response

##### Success

```json
{
  "code": 200,
  "msg": "Thiết lập mật khẩu két sắt thành công"
}
```

##### Failure

```json
{
  "code": 400,
  "error": "invalid-password",
  "message": "Password must be at least 6 characters"
```

- **Các mã lỗi phổ biến**:
  - **8000**: safe box không tồn tại
  - **8001**: Mật khẩu không đúng
  - **8002**: Không đủ tiền
  - **8003**: Không thoả mãn điều kiện sử dụng két sắt (level > 7 hoặc phải là VIP)
  - **8004**: Két sắt đã được thiết lập
  - **8005**: bị khoá do nhập sai quá nhiều lần (5 lần), sẽ bị lock 1 khoảng thời gian (24 giờ)

---

### 56.1. Hủy mật khẩu két sắt

hàm thực hiện hủy mật khẩu két sắt của người chơi

#### Request

- **CMD**: `game.safeboxHandler.unsetPassword`
- **Payload Parameters**:
  - **password**: mật khẩu hiện tại của két sắt

#### Response

##### Success

```json
{
  "code": 200,
  "msg": "Xóa mật khẩu két sắt thành công"
}
```

##### Failure

```json
{
  "code": 400,
  "error": "invalid-password",
  "message": "Password must be at least 6 characters"
}
```

---

### 56.2. kiểm tra trạng thái thiết lập mật khẩu két sắt hay chưa

- Kiểm tra xem đã thiết lập két sắt chưa
- Nếu có rồi: sẽ trả về số tiền đang có
- Nếu chưa có sẽ thông báo chưa thiết lập

#### Request

- **CMD**: `game.safeboxHandler.checkSafeBoxStatus`
- **Payload Parameters**: không cần truyền tham số
- **Example**:
  ```json
  {}
  ```

#### Response

##### Success

```json
{
  "code": 200,
  "msg": "Bạn đã thiết lập két sắt",
  "data": {
    "balance": 890000,
    "isAuthenticated": true // false: không sử dụng pass, true là có sử dụng pass
  }
}
```

---

### 57. Cập nhật mật khẩu két sắt

#### Request

- **CMD**: `game.safeboxHandler.updatePassword`
- **Payload Parameters**:
  - **oldPassword**: Mật khẩu cũ
  - **newPassword**: Mật khẩu mới
    **Example**:
  ```json
  {
    "oldPassword": "123456",
    "newPassword": "12345678"
  }
  ```

#### Response

```json
{
  "code": 200,
  "msg": "Cập nhật mật khẩu két sắt thành công"
}
```

- **Các mã lỗi phổ biến**:
- **8000**: safe box không tồn tại
- **8001**: Mật khẩu không đúng
- **8002**: Không đủ tiền
- **8003**: Không thoả mãn điều kiện sử dụng két sắt (level > 7 hoặc phải là VIP)
- **8004**: Két sắt đã được thiết lập
- **8005**: bị khoá do nhập sai quá nhiều lần (5 lần), sẽ bị lock 1 khoảng thời gian (24 giờ)

---

### 58. Gửi tiền vào két sắt

#### Request

- **CMD**: `game.safeboxHandler.deposit`
- **Payload Parameters**:

  - **amount**: Số tiền muốn gửi vào két sắt

- **Example**:
  ```json
  {
    "amount": 1000000
  }
  ```

#### Response

```json
{
  "code": 200,
  "msg": "Gửi tiền vào két sắt thành công",
  "data": {
    "amount": 1000000, // số tiền cất vào két
    "balance": 1900000, // số tiền còn lại sau khi cất
    "safeBoxBalance": 1000000 // số tiền trong két sắt
  }
}
```

- **Các mã lỗi phổ biến**:
- **8000**: safe box không tồn tại
- **8001**: Mật khẩu không đúng
- **8002**: Không đủ tiền
- **8003**: Không thoả mãn điều kiện sử dụng két sắt (level > 7 hoặc phải là VIP)
- **8004**: Két sắt đã được thiết lập
- **8005**: bị khoá do nhập sai quá nhiều lần (5 lần), sẽ bị lock 1 khoảng thời gian (24 giờ)
- **8006**: Chưa thiết lập mật khẩu cho két sắt

---

### 59. Rút tiền từ két sắt

#### Request

- **CMD**: `game.safeboxHandler.withdraw`
- **Payload Parameters**:
  - **amount**: Số tiền muốn rút ra
  - **password**: Mật khẩu két sắt (tùy chọn, hệ thống sẽ check nếu người dùng đã set pass thì sẽ check, không thì thôi)
    **Example**:
  ```json
  {
    "amount": 1000000,
    "password": "123456"
  }
  ```

#### Response

```json
{
  "code": 200,
  "msg": "Rút tiền từ két sắt thành công",
  "data": {
    "amount": 1000000, // số tiền rút ra
    "balance": 1900000, // số tiền còn lại sau khi rút
    "safeBoxBalance": 1000000 // số tiền trong két sắt
  }
}
```

- **Các mã lỗi phổ biến**:
- **8000**: safe box không tồn tại
- **8001**: Mật khẩu không đúng
- **8002**: Không đủ tiền
- **8003**: Không thoả mãn điều kiện sử dụng két sắt (level > 7 hoặc phải là VIP)
- **8004**: Két sắt đã được thiết lập

---

### 60. Xem lịch sử giao dịch của két sắt

#### Request

- **CMD**: `game.safeboxHandler.getTransactions`
- **Payload Parameters**:
  - **page**: Số trang
  - **limit**: Số lượng bản ghi trên 1 trang
  - **password**: Mật khẩu két sắt
  - **Example**:
  ```json
  {
    "password": "123456",
    "page": 1,
    "limit": 10
  }
  ```

#### Response

```json
{
  "code": 200,
  "msg": "Lịch sử giao dịch két sắt",
  "data": {
    "total": 2,
    "page": 1,
    "limit": 10,
    "totalPages": 1,
    "transactions": [
      {
        "id": 2,
        "player_id": 5,
        "type": "WITHDRAW",
        "amount": 1000000,
        "status": "SUCCESS",
        "note": "Rút tiền từ két sắt",
        "created_at": "2025-05-11 21:10:35"
      },
      {
        "id": 1,
        "player_id": 5,
        "type": "DEPOSIT",
        "amount": 1900000,
        "status": "SUCCESS",
        "note": "Gửi tiền vào két sắt",
        "created_at": "2025-05-11 21:04:12"
      }
    ]
  }
}
```

---

### 61. Xem số tiền trong két sắt đang có

#### Request

- **CMD**: `game.safeboxHandler.balance`
- **Payload Parameters**:
  - **password**: Mật khẩu két sắt
    **Example**:
  ```json
  {
    "password": "123456"
  }
  ```

#### Response

```json
{
  "code": 200,
  "msg": "Thông tin két sắt",
  "data": { "balance": 900000, "isAuthenticated": true }
}
```

---

### 62. Đổi mật khẩu két sắt

#### Request

- **CMD**: `game.safeboxHandler.changePassword`
- **Payload Parameters**:
  - **oldPassword**: Mật khẩu cũ
  - **newPassword**: Mật khẩu mới
    **Example**:
  ```json
  {
    "oldPassword": "123456",
    "newPassword": "12345678"
  }
  ```

#### Response

```json
{
  "code": 200,
  "msg": "Đổi mật khẩu két sắt thành công"
}
```

---

### 63. Lấy danh sách bài viết (tin tức/Sự kiện)

#### Request

- **CMD**: `game.articleHandler.getArticles`
- **Payload Parameters**:
  - **page**: Số trang, mặc định = 1
  - **limit**: Số lượng bản ghi trên 1 trang, mặc định = 10
  - **type**: Loại tin tức (EVENT/ARTICLE) (không truyền gì thì sẽ lấy tất cả)
- **Example**:
  ```json
  {
    "page": 1,
    "limit": 10,
    "type": "EVENT"
  }
  ```

#### Response

```json
{
  "code": 200,
  "data": [
    {
      "id": 2,
      "title": "fsadfas42222222 (Bản sao) (Bản sao)",
      "content": "<p>dfasfasdf</p>",
      "thumbnail": null,
      "type": "EVENT",
      "promoted": false
    },
    {
      "id": 1,
      "title": "Harry TEST",
      "content": "<p>dfasfasdf</p>",
      "thumbnail": null,
      "type": "ARTICLE",
      "promoted": false
    }
  ],
  "total": 2,
  "page": 1,
  "limit": 10
}
```

---

### 64. Lấy chi tiết 1 bài viết

#### Request

- **CMD**: `game.articleHandler.getDetail`
- **Payload Parameters**:
  - **id**: ID bài viết
- **Example**:
  ```json
  {
    "id": 1
  }
  ```

#### Response

```json
{
  "code": 200,
  "data": {
    "id": 1,
    "title": "Harry TEST",
    "content": "<p>dfasfasdf</p>",
    "thumbnail": null,
    "type": "ARTICLE",
    "promoted": false
  }
}
```

---

### 65. Lấy danh sách faqs

#### Request

- **CMD**: `game.supportHandler.getFaqs`
- **Payload Parameters**: không gửi gì lên => `{}`

#### Response

- các type:
  - HOT_ISSUES: Câu hỏi thường gặp
  - DEPOSIT_ISSUES: Câu hỏi về nạp tiền
  - ACCOUNT_ISSUES: Câu hỏi về tài khoản
  - LOGIN_REWARDS: Câu hỏi về thưởng đăng nhập

```json
{
  "code": 200,
  "data": {
    "HOT_ISSUES": [
      {
        "id": 1,
        "question": "Làm thế nào để chơi Poker?",
        "answer": "Bạn cần đăng ký tài khoản và vào bàn chơi..."
      }
    ],
    "DEPOSIT_ISSUES": [
      {
        "id": 2,
        "question": "Quy tắc chia bài là gì?",
        "answer": "Mỗi người chơi được chia 2 lá bài riêng..."
      }
    ],
    "ACCOUNT_ISSUES": [{ "id": 3, "question": "Test", "answer": "Ok 1" }],
    "LOGIN_REWARDS": [{ "id": 4, "question": "Test 2", "answer": "Ok 2" }]
  }
}
```

---

### 66. Gửi phản hồi

#### Request

- **CMD**: `game.supportHandler.sendFeedback`
- **Payload Parameters**:
  - **message**: Nội dung phản hồi
  - **type**: Loại phản hồi
    - DEPOSIT_ISSUES: Vấn đề nạp chip
    - ACCOUNT_ISSUES: Vấn đề tài khoản
    - GAME_BUGS: Lỗi game
    - SUGGESTIONS: Ý kiến
  - **attachments**: Danh sách các file đính kèm, đây là 1 mảng các url sẽ upload qua 1 endpoint http (sẽ update sau)
- **Example**:

```json
{
  "message": "Nội dung phản hồi",
  "type": "DEPOSIT_ISSUES",
  "attachments": []
}
```

#### Response

```json
{
  "code": 200,
  "msg": "Gửi phản hồi thành công"
}
```

---

### 67. Đánh dấu hoàn thành giáo trình

Cập nhật trạng thái hoàn thành giáo trình cho người chơi.

#### Request

- **CMD**: `game.userHandler.completeTutorial`
- **Payload Parameters**: Không cần tham số
- **Example**:
  ```json
  {}
  ```

#### Response

##### Success

```json
{
  "code": 200,
  "msg": {
    "player_id": 123,
    "tutorial_completed": true
  }
}
```

##### Failure

```json
{
  "code": 500,
  "error": "Error updating tutorial status"
}
```

---

### 68. Tip dealer

#### Request

- **CMD**: `game.userHandler.tipDealer`
- **Payload Parameters**:
  - **amount**: Số tiền tip
  - **type**: Loại tip (CHIP, PROPS), hiện đang chi hỗ trợ TIP CHIP (PROPS bao giờ có kịch bản design sẽ làm tiếp)
- **Example**:
  ```json
  {
    "amount": 1000,
    "type": "CHIP"
  }
  ```

#### Response

```json
{
  "code": 200,
  "msg": "Tip dealer thành công",
  "data": {
    // thông tin player và properties
  }
}
```

Gửi kèm cả notification onUpdateMyself

---

## III. Event Listener

### 1. onJoinTable

Event liên quan đến các thông tin ra/vào bàn chơi

### 2. onJoinGame

Event liên quan đến các thông tin cho nhóm người chơi đang ngồi xuống bàn

### 3. onTableEvent

Event liên quan đến luồng xử lý 1 ván chơi qua các vòng

### 4. onUpdateUsers

Event liên quan đến người chơi đang login

### 5. onClearTable

Event khi kết thúc ván và chuẩn bị ván mới

### 6. onEndTurn

Event vòng cuối kết thúc ván chơi

### 6.1. onEndGame

Event kết thúc ván chơi

### 7. onKickUser

Event liên quan đến người chơi bị kích ra khỏi game về lý do gì đó

### 8. onInvitePlay

Event mời chơi

- **Example**:

```json
{
  "msg": {
    "sender": {
      "id": "365e107f-5a34-4b8f-b6ec-25e7a8f6e37e",
      "user_id": "365e107f-5a34-4b8f-b6ec-25e7a8f6e37e",
      "uid": 138451635,
      "full_name": "Hạnh Trang Lâm",
      "username": "dvminh5",
      "avatar": "1"
    },
    "table": {
      "id": "0779f620-abad-11ef-9cd4-f78fa363cc85",
      "zone": "TS",
      "smallBlind": 100,
      "bigBlind": 200,
      "minBuyIn": 2000,
      "maxBuyIn": 40000,
      "minPlayers": 2,
      "maxPlayers": 5,
      "gameMode": "normal"
    }
  }
}
```

### 9. onUpdateMyself

Event liên quan đến dữ liệu người chơi bị thay đổi trong và ngoài bàn chơi

---

> Code: `1009` - Thông báo nhận thưởng CHIPS

- Dữ liệu sẽ được trả về sau 10s khi người chơi đăng nhập
- Tình huống người chơi được thưởng đăng nhập ngày, handler => mở popup thông báo được nhận thưởng, bấm nút nhận => hiệu ứng chip thay đổi (có ani chip bay về)
- Cũng được sử dụng khi người chơi nhận thưởng CHIPS từ thành tích

**Luồng xử lý**:

1. Lấy số dư hiện tại của người chơi
2. Cộng giá trị phần thưởng vào số dư
3. Cập nhật số dư mới
4. Ghi log với thông tin số dư trước và sau khi nhận thưởng
5. Gửi thông báo với thông tin chi tiết

```json
{
  "msg": {
    "type": 1009,
    "description": "Bạn nhận được phần thưởng đăng nhập 3000000 chips vào ngày 2024-12-06 00:33:30",
    "user_id": "365e107f-5a34-4b8f-b6ec-25e7a8f6e37e",
    "amount": 3000000,
    "timestamp": 1733420907,
    "balance": 5000000, // Số dư hiện tại sau khi nhận thưởng
    "badge_id": 1, // Chỉ có khi nhận thưởng từ thành tích
    "badge_name": "Thành tích 1", // Chỉ có khi nhận thưởng từ thành tích
    "reward_type": "CHIPS", // Chỉ có khi nhận thưởng từ thành tích
    "reward_value": 3000000 // Chỉ có khi nhận thưởng từ thành tích
  },
  "type": "onUpdateMyself",
  "code": 1009
}
```

---

> Code: `1010` - Thông báo nhận thưởng EXP

- Được sử dụng khi người chơi nhận thưởng EXP từ thành tích hoặc các hoạt động khác

**Luồng xử lý**:

1. Lấy giá trị EXP hiện tại của người chơi
2. Cộng giá trị phần thưởng vào EXP hiện tại
3. Cập nhật giá trị EXP mới
4. Ghi log với thông tin EXP trước và sau khi nhận thưởng
5. Gửi thông báo với thông tin chi tiết

```json
{
  "msg": {
    "type": 1010,
    "description": "Bạn nhận được phần thưởng 50 EXP từ thành tích: Nâng cấp lần thứ 1",
    "user_id": "123",
    "amount": 50,
    "timestamp": 1733420907,
    "badge_id": 1,
    "badge_name": "Nâng cấp lần thứ 1",
    "exp": 150, // EXP hiện tại sau khi nhận thưởng
    "level": 2, // Level hiện tại
    "reward_type": "EXP",
    "reward_value": 50
  },
  "type": "onUpdateMyself",
  "code": 1010
}
```

---

> Code: `1011` - Thông báo nhận thưởng VIP

- Được sử dụng khi người chơi nhận thưởng điểm VIP từ thành tích hoặc các hoạt động khác

**Luồng xử lý**:

1. Lấy điểm VIP hiện tại của người chơi
2. Cộng giá trị phần thưởng vào điểm VIP hiện tại
3. Cập nhật giá trị điểm VIP mới
4. Ghi log với thông tin điểm VIP trước và sau khi nhận thưởng
5. Gửi thông báo với thông tin chi tiết

```json
{
  "msg": {
    "type": 1011,
    "description": "Bạn nhận được phần thưởng 100 điểm VIP từ thành tích: Thành tích VIP",
    "user_id": "123",
    "amount": 100,
    "timestamp": 1733420907,
    "badge_id": 5,
    "badge_name": "Thành tích VIP",
    "vippoint": 250, // Điểm VIP hiện tại sau khi nhận thưởng
    "reward_type": "VIP",
    "reward_value": 100
  },
  "type": "onUpdateMyself",
  "code": 1011
}
```

---

> Code: `1012` - Thông báo nhận thưởng vật phẩm

- Được sử dụng khi người chơi nhận thưởng vật phẩm (AVATAR, TITLE, DECORATION) từ thành tích

**Luồng xử lý**:

1. Thêm vật phẩm vào tài khoản người chơi
2. Ghi log với thông tin vật phẩm đã thêm
3. Gửi thông báo với thông tin chi tiết

```json
{
  "msg": {
    "type": 1012,
    "description": "Bạn nhận được phần thưởng avatar: special_avatar_1 từ thành tích: Nâng cấp lần thứ 1",
    "user_id": "123",
    "item_type": "AVATAR", // Có thể là AVATAR, TITLE, DECORATION
    "item_value": "special_avatar_1",
    "is_permanent": true,
    "timestamp": 1733420907,
    "badge_id": 1,
    "badge_name": "Nâng cấp lần thứ 1",
    "reward_type": "AVATAR",
    "reward_value": "special_avatar_1"
  },
  "type": "onUpdateMyself",
  "code": 1012
}
```

---

> Code: `1013` - Thông báo nhận thưởng lên level

```json
{
  "msg": {
    "type": 1013,
    "player": {
      "id": 1,
      "user_id": 100253171,
      "uid": 100253171,
      "nick_name": "betapcode",
      "username": "betapcode",
      "full_name": "",
      "avatar": "1",
      "type": "WEB",
      "last_login": 1745857621,
      "win": 17,
      "win_rate": "40.4761904",
      "lose": 0,
      "total": 42,
      "rank": 0,
      "vippoint": 0,
      "level": 3,
      "exp": 28,
      "coin": 0,
      "properties": {
        "id": 1,
        "day_play_num": 0,
        "day_win_num": 0,
        "day_lost_num": 0,
        "spin_num": 1,
        "last_time_spin": 1737099920,
        "continuous_login_num": 1,
        "continuous_spin_num": 0,
        "is_first_pay": false,
        "play_json": "{\"biggestWon\":1391879,\"bestHand\":[\"KC\",\"7H\",\"JS\",\"7D\",\"7S\",\"7C\",\"JH\"],\"codeWin\":607}",
        "small_speaker": 0,
        "big_speaker": 0,
        "tutorial_completed": 0 // trạng thái hoàn thành giáo trình
      }
    },
    "reward": {
      "level": 3,
      "level_title": "Fans Poker",
      "level_key": "LEVEL_3",
      "reward_amount": 10000000,
      "old_balance": 13069211,
      "new_balance": 23069211
    }
  }
}
```

---

> Code: `1008`

- Dữ liệu sẽ được trả về sau 10s khi người chơi đăng nhập
- Tình huống người chơi được cộng thưởng khi đăng ký

---

> Code: `1007`

```json
{ "type": 1007, "msg": "Update Money When End Game", "balance": 15832000 }
```

### 10. onChat

Event về chat trong bàn chơi

### 11. onUserChat

Event về chat 1:1 giữa người chơi với nhau

### 12. disconnect

Event mất kết nối

### 13. close

Event kết nối bị đóng

=======

---

### 14. Event Listener: onNotification

Receives notifications from the server.

#### General Notifications

```json
{
  "notification": {
    "title": "Notification Title",
    "content": "Notification content text",
    "type": "info", // info, warning, error, success, bigwin
    "scope": "table", // table, zone, global
    "tid": "c73883c3-a322-11ef-82f1-9fb710c84ba7", // only for table notifications
    "zone": "TS", // only for zone notifications
    "timestamp": 1733674059000,
    "senderId": 1,
    "senderName": "Admin",
    "senderFullName": "Tonny Tống",
    "senderAvatar": "https://example.com/avatar.jpg"
  }
}
```

---

### 15. Event Listener: onFriends

- Sự kiện cho 2 hành động: gửi cho người chơi gửi yêu cầu kết bạn và đồng ý kết bạn (chấp nhận lời mời)
- Chú ý: nếu người chơi phải online thì mới nhận được notifcation, còn không thì sẽ không gửi event `onFriends`

#### Event Data

```json
{
  "msg": {
    "event": "onFriends",
    "action": "friendRequest",
    "data": {
      "from": {
        "id": 5,
        "user_id": 100253209,
        "name": "dvminh5",
        "avatar": "1"
      },
      "message": "dvminh5 đã gửi lời mời kết bạn cho bạn.",
      "time": 1744994048003
    }
  },
  "type": "onFriends",
  "code": 2005
}
```

```json
{
  "msg": {
    "event": "onFriends",
    "action": "friendAccepted",
    "data": {
      "from": {
        "id": 19,
        "user_id": 100000008,
        "name": "dvminh4",
        "avatar": "1"
      },
      "message": "dvminh4 đã chấp nhận lời mời kết bạn của bạn.",
      "time": 1744994060369
    }
  },
  "type": "onFriends",
  "code": 2006
}
```
