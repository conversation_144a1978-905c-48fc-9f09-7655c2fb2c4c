<!DOCTYPE html>
<html>
  <head>
    <meta charset="utf-8" />
    <title>
      Pomelo
    </title>
    <meta http-equiv="content-type" content="text/html;charset=utf-8" />
    <meta http-equiv="content-style-type" content="text/css" />
    <meta http-equiv="content-scripte-type" content="text/javascript" />
    <meta name="author" content="netease" />
    <meta name="version" content="1.0" />
    <meta name="keywords" content="pomelo" />
    <link type="text/css" rel="stylesheet" href="css/base.css" />
    <script src="js/lib/socket.io.js">
    </script>
    <!-- <script src="js/lib/pomeloclient.js"></script> -->
    <script src="js/lib/build/build.js" type="text/javascript"></script>
    <script type="text/javascript">
      require('boot');
    </script> 
    
    <script type="text/javascript">
      var pomelo = window.pomelo;
      var host = "127.0.0.1"; // "************"; // "127.0.0.1";
      var port = "3014"; // "3010"; "3014";
      function show(deviceId) {
        console.log("start pomelo");
        pomelo.init({
          host: host,
          port: port,
          log: true
        }, function() {
          // get 
          pomelo.request("gate.gateHandler.queryEntry", {uid: 1}, function(data) {
            console.log('dataMINH: ', data);
            const _host = data?.host ?? '';
            const _port = data?.port ?? '';
            pomelo.disconnect();

            pomelo.init({
              host: _host,
              port: _port,
              log: true
            }, function() {
                console.log("start pomelo again");
                // call login: test, minh
                pomelo.request("connector.entryHandler.loginDevice", {deviceId: deviceId}, function(data) {
                  console.log('data: ', data);
                  const userId = data?.player?.user_id ?? '';
                  console.log('- userId: ', userId);

                  // Create table
                  // ----------------------------------------------------------------
                  var dataPost = {
                    "smallBlind":"50",
                    "bigBlind":"100",
                    "minBuyIn":"100",
                    "maxBuyIn":"10000",
                    "minPlayers":"2",
                    "maxPlayers":"10",
                    "gameMode":"normal",
                    "zone": "CC",
                  };
                  const createTable = 'game.tableHandler.createTable';
                  // pomelo.request(createTable, dataPost, function(data) {
                    // console.log('createTable >> data: ', data);
                  // });

                  // Get list tables
                  // pomelo.request('game.tableHandler.getTables', '', function(data) {
                    // console.log('getTables >> data: ', data);
                  // });

                  // Get list tables by zone = TS
                  pomelo.request('game.tableHandler.getTables', {zone: 'TS'}, function(data) {
                    // console.log('getTables with zone = TS >> data: ', data.tables);
                    const tables = data?.tables?.tables ?? [];
                    console.log('getTables with zone = TS >> tables: ', tables);
                    if (tables.length > 0) {
                      // const randomTable = tables[Math.floor(Math.random() * tables.length)];
                      const randomTable = tables[0];
                      console.log('Random table: ', randomTable);
                      
                      // join table
                      const tid = randomTable?.id ?? null;
                      const minBuyIn = randomTable?.minBuyIn ?? 0;
                      const maxBuyIn = randomTable?.maxBuyIn ?? 0;

                      const cmdJoinTable = 'game.tableHandler.joinTable';
                      pomelo.request(cmdJoinTable, {tid: tid}, function(data) {
                        console.log('joinTable >> data: ', data);

                        // joinGame: ngồi xuống bàn
                        const cmdJoinGame = 'game.tableHandler.joinGame';
                        const buyIn = maxBuyIn - minBuyIn;

                        console.log('-> maxBuyIn: ', maxBuyIn);
                        console.log('-> minBuyIn: ', minBuyIn);
                        console.log('-> buyIn: ', buyIn);
                        pomelo.request(cmdJoinGame, {buyIn: buyIn, index: 1}, function(data) {
                          console.log('joinGame >> data: ', data);
                          if (Number(data.code) === 200) {
                            // Command đứng dậy
                            // =================================================================
                            const cmdStandUp = 'game.tableHandler.standUp';
                            pomelo.request(cmdStandUp, '', function(data) {
                              console.log('standUp >> data: ', data);
                            });
                            // =================================================================

                            // Command Get Thông tin 1 user
                            // =================================================================
                            /*
                            const cmdGetUserInfo = 'game.userHandler.getUserInfo';
                            pomelo.request(cmdGetUserInfo, {uid: userId}, function(data) {
                              console.log('- getUserInfo >> data: ', data);
                            });
                            */
                            // =================================================================

                            // Command: Get danh sách users đang ở lobby để mời chời
                            // =================================================================
                            const cmdGetUsersInLobby = 'game.tableHandler.getUsersInLobby';
                            pomelo.request(cmdGetUsersInLobby, {uid: userId}, function(data) {
                              console.log('- getUsersInLobby >> data: ', data);
                            });
                            // =================================================================

                            // Thoát khỏi phòng
                            // =================================================================
                            /*
                            const cmdLeaveTable = 'game.tableHandler.leaveTable';
                            pomelo.request(cmdLeaveTable, '', function(data) {
                              console.log('leaveTable >> data: ', data);
                              
                              if (Number(data.code) === 200) {
                                console.log('Leave table successfully => chuẩn bị logout');
                                // send command: Logout
                                // =================================================================
                                const cmdLogout = 'connector.entryHandler.logout';
                                console.log('Logout with userId: ', userId);
                                pomelo.request(cmdLogout, {uid: userId}, function(dt) {
                                  console.log('logout >> data: ', dt);
                                });
                                // =================================================================
                              }
                              
                            });
                            */
                            // =================================================================
                          }
                        });

                      });

                    } else {
                      console.log('No tables available');
                    }
                  });

                  // Get list rooms
                  // const cmdListRooms = 'game.tableHandler.getRooms';
                  // pomelo.request(cmdListRooms, '', function(data) {
                  //  console.log('getRooms >> data: ', data);
                  // });
                  
                }); // end handler loginDevice
            });
          });
        });
      } // end show

      // show 2
      function show2(deviceId) {
        console.log("start pomelo");
        pomelo.init({
          host: host,
          port: port,
          log: true
        }, function() {
          
          pomelo.request("gate.gateHandler.queryEntry", {uid: 1}, function(data) {

            console.log('dataMINH: ', data);
            const _host = data?.host ?? '';
            const _port = data?.port ?? '';
            pomelo.disconnect();

            pomelo.init({
              host: _host,
              port: _port,
              log: true
            }, function() {

              pomelo.request("connector.entryHandler.loginDevice", {deviceId: deviceId}, function(data) {
                console.log('data: ', data);
                const userId = data?.player?.user_id ?? '';
                console.log('- userId: ', userId);

                // Get list tables by zone = TS
                pomelo.request('game.tableHandler.getTables', {zone: 'TS'}, function(data) {
                  // console.log('getTables with zone = TS >> data: ', data.tables);
                  const tables = data?.tables?.tables ?? [];
                  console.log('getTables with zone = TS >> tables: ', tables);
                  if (tables.length > 0) {
                    const randomTable = tables[Math.floor(Math.random() * tables.length)];
                    console.log('Random table: ', randomTable);
                    
                    // join table
                    const tid = randomTable?.id ?? null;
                    const minBuyIn = randomTable?.minBuyIn ?? 0;
                    const maxBuyIn = randomTable?.maxBuyIn ?? 0;

                  } else {
                    console.log('No tables available');
                  }
                });
              });
            });
          });
      
        });
      } // end login to lobby
      
      function switchTable() {
        console.log("start pomelo switchTable");
        pomelo.init({
          host: host,
          port: port,
          log: true
        }, function() {

          pomelo.request("gate.gateHandler.queryEntry", {uid: 1}, function(data) {
            console.log('switchTable dataMINH: ', data);
            const _host = data?.host ?? '';
            const _port = data?.port ?? '';
            pomelo.disconnect();
            
            pomelo.init({
              host: _host,
              port: _port,
              log: true
            }, function() {
              // call login: test, minh
              pomelo.request("connector.entryHandler.loginDevice", {deviceId: "minh5"}, function(data) {
                console.log('data: ', data);
                const userId = data?.player?.user_id ?? '';
                console.log('- userId: ', userId);
    
                // Create table
                // ----------------------------------------------------------------
                var dataPost = {
                  "smallBlind":"50",
                  "bigBlind":"100",
                  "minBuyIn":"100",
                  "maxBuyIn":"10000",
                  "minPlayers":"2",
                  "maxPlayers":"10",
                  "gameMode":"normal",
                  "zone": "CC",
                };
                // const createTable = 'game.tableHandler.createTable';
                // pomelo.request(createTable, dataPost, function(data) {
                  // console.log('createTable >> data: ', data);
                // });
    
                // Get list tables
                // pomelo.request('game.tableHandler.getTables', '', function(data) {
                  // console.log('getTables >> data: ', data);
                // });
    
                // Get list tables by zone = TS
                pomelo.request('game.tableHandler.getTables', {zone: 'TS'}, function(data) {
                  // console.log('getTables with zone = TS >> data: ', data.tables);
                  const tables = data?.tables?.tables ?? [];
                  console.log('getTables with zone = TS >> tables: ', tables);
                  if (tables.length > 0) {
                    // const randomTable = tables[Math.floor(Math.random() * tables.length)];
                    const randomTable = tables[0];
                    console.log('Get first table: ', randomTable);
                    
                    // join table
                    const tid = randomTable?.id ?? null;
                    const minBuyIn = randomTable?.minBuyIn ?? 0;
                    const maxBuyIn = randomTable?.maxBuyIn ?? 0;
    
                    const cmdJoinTable = 'game.tableHandler.joinTable';
                    pomelo.request(cmdJoinTable, {tid: tid}, function(data) {
                      console.log('joinTable >> data: ', data);
    
                      // joinGame: ngồi xuống bàn
                      const cmdJoinGame = 'game.tableHandler.joinGame';
                      const buyIn = maxBuyIn - minBuyIn;
    
                      console.log('-> maxBuyIn: ', maxBuyIn);
                      console.log('-> minBuyIn: ', minBuyIn);
                      console.log('-> buyIn: ', buyIn);
                      pomelo.request(cmdJoinGame, {buyIn: buyIn, index: 1}, function(data) {
                        console.log('joinGame >> data: ', data);
                        if (Number(data.code) === 200) {
                          // Command đứng dậy
                          // =================================================================
                          const cmdStandUp = 'game.tableHandler.standUp';
                          pomelo.request(cmdStandUp, '', function(data) {
                            console.log('standUp >> data: ', data);
                          });
                          // =================================================================
    
                          // Command switchTable
                          // =================================================================
                          const cmdSwitchTable = 'game.tableHandler.switchTable';
                          pomelo.request(cmdSwitchTable, '', function(data) {
                            console.log('switchTable >> data: ', data);
                          });
                          // =================================================================
    
                          // Command Get Thông tin 1 user
                          // =================================================================
                          /*
                          const cmdGetUserInfo = 'game.userHandler.getUserInfo';
                          pomelo.request(cmdGetUserInfo, {uid: userId}, function(data) {
                            console.log('- getUserInfo >> data: ', data);
                          });
                          */
                          // =================================================================
    
                          // Command: Get danh sách users đang ở lobby để mời chời
                          // =================================================================
                          /*
                          const cmdGetUsersInLobby = 'game.tableHandler.getUsersInLobby';
                          pomelo.request(cmdGetUsersInLobby, {uid: userId}, function(data) {
                            console.log('- getUsersInLobby >> data: ', data);
                          });
                          */
                          // =================================================================
    
                          // Thoát khỏi phòng
                          // =================================================================
                          /*
                          const cmdLeaveTable = 'game.tableHandler.leaveTable';
                          pomelo.request(cmdLeaveTable, '', function(data) {
                            console.log('leaveTable >> data: ', data);
                            
                            if (Number(data.code) === 200) {
                              console.log('Leave table successfully => chuẩn bị logout');
                              // send command: Logout
                              // =================================================================
                              const cmdLogout = 'connector.entryHandler.logout';
                              console.log('Logout with userId: ', userId);
                              pomelo.request(cmdLogout, {uid: userId}, function(dt) {
                                console.log('logout >> data: ', dt);
                              });
                              // =================================================================
                            }
                            
                          });
                          */
                          // =================================================================
                        }
                      });
    
                    });
    
                  } else {
                    console.log('No tables available');
                  }
                });
    
                // Get list rooms
                // const cmdListRooms = 'game.tableHandler.getRooms';
                // pomelo.request(cmdListRooms, '', function(data) {
                //  console.log('getRooms >> data: ', data);
                // });
                
    
    
              });

            });
          });
        });
      } // end switchTable          

      function findTable() {
        console.log("start pomelo");
        pomelo.init({
          host: host,
          port: port,
          log: true
        }, function() {
          // call login: test, minh
          pomelo.request("connector.entryHandler.loginDevice", {deviceId: "minh5"}, function(data) {
            console.log('data: ', data);
            const userId = data?.player?.user_id ?? '';
            console.log('- userId: ', userId);

            // Get find table
            // --------------------------------------------------------------------------------
            const dataFindPost = {
              "smallBlind":"50",
              "bigBlind":"100",
              "minBuyIn":"100",
              "maxBuyIn":"10000",
              "maxPlayers":"10",
              "gameMode":"normal",
              "zone": "CC",
            };
            
            pomelo.request('game.tableHandler.findTable', dataFindPost, function(data) {
              console.log('- findTable >> data: ', data);
            });
            // End: Find Table ----------------------------------------------------------------
          });
        });
      } // end findTable

      function chatInTable() {
        console.log("start pomelo");
        pomelo.init({
          host: host,
          port: port,
          log: true
        }, function() {
          // call login: test, minh
          pomelo.request("connector.entryHandler.loginDevice", {deviceId: "minh"}, function(data) {
            console.log('data: ', data);
            const userId = data?.player?.user_id ?? '';
            console.log('- userId: ', userId);

            // Get list table
            // --------------------------------------------------------------------------------
            // Get list tables by zone = TS
            pomelo.request('game.tableHandler.getTables', {zone: 'TS'}, function(data) {

              const tables = data?.tables?.tables ?? [];
              console.log('getTables with zone = TS >> tables: ', tables);
              if (tables.length > 0) {
                // const randomTable = tables[Math.floor(Math.random() * tables.length)];
                const randomTable = tables[0];
                console.log('First table: ', randomTable);
                
                // joinTable
                // ----------------------------------------------------------------------------------
                const tid = randomTable?.id ?? null;
                const minBuyIn = randomTable?.minBuyIn ?? 0;
                const maxBuyIn = randomTable?.maxBuyIn ?? 0;
                const cmdJoinTable = 'game.tableHandler.joinTable';
                pomelo.request(cmdJoinTable, {tid: tid}, function(data) {
                  console.log(' - joinTable >> data: ', data);

                  // joinGame: ngồi xuống bàn
                  // --------------------------------------------------------------------------------
                  const cmdJoinGame = 'game.tableHandler.joinGame';
                  const buyIn = maxBuyIn - minBuyIn;

                  console.log('-> maxBuyIn: ', maxBuyIn);
                  console.log('-> minBuyIn: ', minBuyIn);
                  console.log('-> buyIn: ', buyIn);
                  pomelo.request(cmdJoinGame, {buyIn: buyIn, index: 1}, function(data) {
                    console.log('- joinGame >> data: ', data);
                    if (Number(data.code) === 200) {
                      // Command: sendChat
                      const cmdSendChat = 'chat.chatHandler.sendMessage';
                      const message = 'Hello, I am Minh';
                      pomelo.request(cmdSendChat, {content: message, target  : "table"}, function(data) {
                        console.log('- sendChat >> data: ', data);
                      });
                    }
                  });
                  // End: joinGame ------------------------------------------------------------------
                });
                // End: joinTable -------------------------------------------------------------------
              } else {
                console.log('No tables available');
              }
            });
            // End: list Table ----------------------------------------------------------------
          });
        });
      } // end findTable

      function playerInGame(deviceIdName, positionIndex) {
        console.log("start pomelo");
        pomelo.init({
          host: host,
          port: port,
          log: true
        }, function() {

          pomelo.request("gate.gateHandler.queryEntry", {uid: 1}, function(data) {

            console.log('dataMINH: ', data);
            const _host = data?.host ?? '';
            const _port = data?.port ?? '';
            pomelo.disconnect();

            pomelo.init({
              host: _host,
              port: _port,
              log: true
            }, function() {

              // call login: test, minh
              pomelo.request("connector.entryHandler.loginDevice", {deviceId: deviceIdName}, function(data) {
                console.log('data player: ', data);
                const userId = data?.player?.user_id ?? '';
                console.log('- userId: ', userId);

                // Create table
                // ----------------------------------------------------------------
                var dataPost = {
                  "smallBlind":"50",
                  "bigBlind":"100",
                  "minBuyIn":"100",
                  "maxBuyIn":"10000",
                  "minPlayers":"2",
                  "maxPlayers":"10",
                  "gameMode":"normal",
                  "zone": "CC",
                };
                // const createTable = 'game.tableHandler.createTable';
                // pomelo.request(createTable, dataPost, function(data) {
                  // console.log('createTable >> data: ', data);
                // });

                // Get list tables
                // pomelo.request('game.tableHandler.getTables', '', function(data) {
                  // console.log('getTables >> data: ', data);
                // });

                // Get list tables by zone = TS
                pomelo.request('game.tableHandler.getTables', {zone: 'TS'}, function(data) {
                  // console.log('getTables with zone = TS >> data: ', data.tables);
                  const tables = data?.tables?.tables ?? [];
                  console.log('getTables with zone = TS >> tables: ', tables);
                  if (tables.length > 0) {
                    // const randomTable = tables[Math.floor(Math.random() * tables.length)];
                    const randomTable = tables[0];
                    console.log('Random table: ', randomTable);
                    
                    // join table
                    // const tid = "55f6e210-e7d8-11ef-a01a-19400bd09aa1"; // randomTable?.id ?? null;
                    const tid = randomTable?.id ?? null;
                    const minBuyIn = randomTable?.minBuyIn ?? 0;
                    const maxBuyIn = randomTable?.maxBuyIn ?? 0;

                    const cmdJoinTable = 'game.tableHandler.joinTable';
                    pomelo.request(cmdJoinTable, {tid: tid}, function(data) {
                      console.log('joinTable >> data: ', data);

                      // joinGame: ngồi xuống bàn
                      const cmdJoinGame = 'game.tableHandler.joinGame';
                      const buyIn = maxBuyIn - minBuyIn;

                      console.log('-> maxBuyIn: ', maxBuyIn);
                      console.log('-> minBuyIn: ', minBuyIn);
                      console.log('-> buyIn: ', buyIn);
                      pomelo.request(cmdJoinGame, {buyIn: buyIn, index: positionIndex}, function(data) {
                        console.log('joinGame >> data: ', data);
                        if (Number(data.code) === 200) {
                          // Command đứng dậy
                          // =================================================================
                          // const cmdStandUp = 'game.tableHandler.standUp';
                          // pomelo.request(cmdStandUp, '', function(data) {
                            // console.log('standUp >> data: ', data);
                          // });
                          // =================================================================

                          // Command Get Thông tin 1 user
                          // =================================================================
                          /*
                          const cmdGetUserInfo = 'game.userHandler.getUserInfo';
                          pomelo.request(cmdGetUserInfo, {uid: userId}, function(data) {
                            console.log('- getUserInfo >> data: ', data);
                          });
                          */
                          // =================================================================

                          // Command: Get danh sách users đang ở lobby để mời chời
                          // =================================================================
                          // const cmdGetUsersInLobby = 'game.tableHandler.getUsersInLobby';
                          // pomelo.request(cmdGetUsersInLobby, {uid: userId}, function(data) {
                            // console.log('- getUsersInLobby >> data: ', data);
                          // });
                          // =================================================================
                        }
                      });

                    });

                  } else {
                    console.log('No tables available');
                  }
                });

                // Get list rooms
                // const cmdListRooms = 'game.tableHandler.getRooms';
                // pomelo.request(cmdListRooms, '', function(data) {
                //  console.log('getRooms >> data: ', data);
                // });
                
              });
            
            });

          });

        });
      } // end playerInGame

      function playerInGame2(deviceIdName, positionIndex) {
        console.log("start pomelo");
        pomelo.init({
          host: host,
          port: port,
          log: true
        }, function() {

          pomelo.request("gate.gateHandler.queryEntry", {uid: 1}, function(data) {

            console.log('dataMINH: ', data);
            const _host = data?.host ?? '';
            const _port = data?.port ?? '';
            pomelo.disconnect();

            pomelo.init({
              host: _host,
              port: _port,
              log: true
            }, function() {

              // call login: test, minh
              pomelo.request("connector.entryHandler.loginDevice", {deviceId: deviceIdName}, function(data) {
                console.log('data player: ', data);
                const userId = data?.player?.user_id ?? '';
                console.log('- userId: ', userId);

                // Create table
                // ----------------------------------------------------------------
                var dataPost = {
                  "smallBlind":"50",
                  "bigBlind":"100",
                  "minBuyIn":"100",
                  "maxBuyIn":"10000",
                  "minPlayers":"2",
                  "maxPlayers":"10",
                  "gameMode":"normal",
                  "zone": "CC",
                };
                // const createTable = 'game.tableHandler.createTable';
                // pomelo.request(createTable, dataPost, function(data) {
                  // console.log('createTable >> data: ', data);
                // });

                // Get list tables
                // pomelo.request('game.tableHandler.getTables', '', function(data) {
                  // console.log('getTables >> data: ', data);
                // });

                // Get list tables by zone = TS
                pomelo.request('game.tableHandler.getTables', {zone: 'TS'}, function(data) {
                  // console.log('getTables with zone = TS >> data: ', data.tables);
                  const tables = data?.tables?.tables ?? [];
                  console.log('getTables with zone = TS >> tables: ', tables);
                  if (tables.length > 0) {
                    // const randomTable = tables[Math.floor(Math.random() * tables.length)];
                    const randomTable = tables[0];
                    console.log('Random table: ', randomTable);
                    
                    // join table
                    const tid = "55f6e210-e7d8-11ef-a01a-19400bd09aa1"; // randomTable?.id ?? null;
                    const minBuyIn = randomTable?.minBuyIn ?? 0;
                    const maxBuyIn = randomTable?.maxBuyIn ?? 0;

                    const cmdJoinTable = 'game.tableHandler.joinTable';
                    pomelo.request(cmdJoinTable, {tid: tid}, function(data) {
                      console.log('joinTable >> data: ', data);

                      // joinGame: ngồi xuống bàn
                      const cmdJoinGame = 'game.tableHandler.joinGame';
                      const buyIn = maxBuyIn - minBuyIn;

                      console.log('-> maxBuyIn: ', maxBuyIn);
                      console.log('-> minBuyIn: ', minBuyIn);
                      console.log('-> buyIn: ', buyIn);
                      pomelo.request(cmdJoinGame, {buyIn: buyIn, index: positionIndex}, function(data) {
                        console.log('joinGame >> data: ', data);
                        if (Number(data.code) === 200) {
                          // Command đứng dậy
                          // =================================================================
                          const cmdStandUp = 'game.tableHandler.standUp';
                          pomelo.request(cmdStandUp, '', function(data) {
                            console.log('standUp >> data: ', data);
                          });
                          // =================================================================

                          // Command Get Thông tin 1 user
                          // =================================================================
                          /*
                          const cmdGetUserInfo = 'game.userHandler.getUserInfo';
                          pomelo.request(cmdGetUserInfo, {uid: userId}, function(data) {
                            console.log('- getUserInfo >> data: ', data);
                          });
                          */
                          // =================================================================

                          // Command: Get danh sách users đang ở lobby để mời chời
                          // =================================================================
                          // const cmdGetUsersInLobby = 'game.tableHandler.getUsersInLobby';
                          // pomelo.request(cmdGetUsersInLobby, {uid: userId}, function(data) {
                            // console.log('- getUsersInLobby >> data: ', data);
                          // });
                          // =================================================================
                        }
                      });

                    });

                  } else {
                    console.log('No tables available');
                  }
                });

                // Get list rooms
                // const cmdListRooms = 'game.tableHandler.getRooms';
                // pomelo.request(cmdListRooms, '', function(data) {
                //  console.log('getRooms >> data: ', data);
                // });
                
              });
            
            });

          });

        });
      } // end playerInGame2

      function loginFacebook(deviceIdName, positionIndex) {
        console.log("start pomelo init loginFacebook");
        pomelo.init({
          host: host,
          port: port,
          log: true
        }, function() {

          pomelo.request("gate.gateHandler.queryEntry", {uid: 1}, function(data) {
            console.log('dataMINH: ', data);
            const _host = data?.host ?? '';
            const _port = data?.port ?? '';
            pomelo.disconnect();
            
            pomelo.init({
              host: _host,
              port: _port,
              log: true
            }, function() {
                console.log("start pomelo login facebook");

                // call login: test, minh
                const dataPostLoginFb = {
                  "accessToken": "EAAHkd00jAT4BO0t0sh4fKDxc39bgAGlZCyODWSMkaVGdOoCeWEXEOGnc57PyxPb72ZCyn8ziTf0yhvPrZCYHZCsZAE3zmnjWZCat5VEazx1hBxEumJCioRtGnhXrv0pLGPeiel6fLilaaYO8f6ZB9boZBr0qGDSQLn3lLlZBXdmpJb3OLB6yd7ZAML1ABNLE4LZAyz98mx1TpPA7n1EFtry6QZDZD",
                  // "facebookId": "3158302040978973",
                  "userInfo": {
                    "id": "3158302040978973",
                    "name": "Bé Tập Code",
                    "email": "<EMAIL>",
                  }
                }
                pomelo.request("connector.entryHandler.loginFacebook", dataPostLoginFb, function(data) {
                  console.log('data login facebook: ', data);
                  // const userId = data?.player?.user_id ?? '';
                  // console.log('- userId: ', userId);
                });

            });
            

          });
          
        });
      } // end loginFacebook

      function loginByEmail() {
        console.log("start pomelo init loginByEmail");
        pomelo.init({
          host: host,
          port: port,
          log: true
        }, function() {

          pomelo.request("gate.gateHandler.queryEntry", {uid: 1}, function(data) {
            console.log('dataMINH: ', data);
            const _host = data?.host ?? '';
            const _port = data?.port ?? '';
            pomelo.disconnect();
            
            pomelo.init({
              host: _host,
              port: _port,
              log: true
            }, function() {
                console.log("start pomelo login by email");
                const dataByEmail = {
                  "email": "<EMAIL>",
                  "password": "p@kee123"
                }
                pomelo.request("connector.entryHandler.loginByEmail", dataByEmail, function(data) {
                  console.log('> data login by email: ', data);
                  // const userId = data?.player?.user_id ?? '';
                  // console.log('- userId: ', userId);
                });

            });
            

          });
          
        });
      } // end loginByEmail

      function actionSenderPlay(deviceIdName, positionIndex) {
        console.log("start pomelo init actionSenderPlay");
        pomelo.init({
          host: host,
          port: port,
          log: true
        }, function() {

          pomelo.request("gate.gateHandler.queryEntry", {uid: 1}, function(data) {
            console.log('dataMINH: ', data);
            const _host = data?.host ?? '';
            const _port = data?.port ?? '';
            pomelo.disconnect();
            
            pomelo.init({
              host: _host,
              port: _port,
              log: true
            }, function() {
                console.log("start pomelo actionSenderPlay");
                const dataByEmail = {
                  "email": "<EMAIL>",
                  "password": "p@kee123"
                }
                pomelo.request("connector.entryHandler.loginByEmail", dataByEmail, function(data) {
                  console.log('data login by email: ', data);
                  pomelo.request('game.tableHandler.getTables', {zone: 'TS'}, function(data) {
                    const tables = data?.tables?.tables ?? [];
                    console.log('getTables with zone = TS >> tables: ', tables);
                    if (tables.length > 0) {
                      const randomTable = tables[0];
                      console.log('id table: ', randomTable);
                        // join table
                        const tid = randomTable?.id ?? null;
                        const minBuyIn = randomTable?.minBuyIn ?? 0;
                        const maxBuyIn = randomTable?.maxBuyIn ?? 0;
                        const cmdJoinTable = 'game.tableHandler.joinTable';
                        pomelo.request(cmdJoinTable, {tid: tid}, function(data) {
                          console.log('joinTable >> data: ', data);
                          // joinGame: ngồi xuống bàn
                          const cmdJoinGame = 'game.tableHandler.joinGame';
                          const buyIn = maxBuyIn - minBuyIn;

                          console.log('-> maxBuyIn: ', maxBuyIn);
                          console.log('-> minBuyIn: ', minBuyIn);
                          console.log('-> buyIn: ', buyIn);
                          pomelo.request(cmdJoinGame, {buyIn: buyIn, index: 1}, function(data) {
                            console.log('joinGame >> data: ', data);
                            if (Number(data.code) === 200) {
                              // mời chơi
                              // =================================================================
                              const cmdInvitePlay = 'game.userHandler.sendInvitePlay';
                              const inviteData = {
                                // "sender_id": "c05b92b4-ca10-447e-9607-735266317119",
                                "tid": tid,
                                "receiver_id": 5,
                                "pos": 2,
                                // "username": "fb3158302040978973",
                              };
                              pomelo.request(cmdInvitePlay, inviteData, function(data) {
                                console.log('sendInvitePlay >> data: ', data);
                              });
                            }
                          });
                        });
                    }
                  });
                });

            });
            

          });
          
        });
      } // end actionSenderPlay

      function actionReceiverPlay(deviceIdName, positionIndex) {
        console.log("start pomelo init actionReceiverPlay");
        pomelo.init({
          host: host,
          port: port,
          log: true
        }, function() {

          pomelo.request("gate.gateHandler.queryEntry", {uid: 1}, function(data) {
            console.log('dataMINH: ', data);
            const _host = data?.host ?? '';
            const _port = data?.port ?? '';
            pomelo.disconnect();
            
            pomelo.init({
              host: _host,
              port: _port,
              log: true
            }, function() {
                console.log("start pomelo actionReceiverPlay");
                // pomelo.request("connector.entryHandler.loginByEmail", dataByEmail, function(data) {
                pomelo.request("connector.entryHandler.loginDevice", {deviceId: 'minh5'}, function(data) {
                  console.log('data login by device: ', data);
                  pomelo.request('game.tableHandler.getTables', {zone: 'TS'}, function(data) {
                    const tables = data?.tables?.tables ?? [];
                    console.log('getTables with zone = TS >> tables: ', tables);
                    if (tables.length > 0) {
                      const randomTable = tables[0];
                      console.log('id table: ', randomTable);
                    }
                  });
                });

            });

            
            
          });
          
        });
      } // end actionReceiverPlay

      function addFriend(deviceIdName, fid) {
        console.log("start pomelo");
        pomelo.init({
          host: host,
          port: port,
          log: true
        }, function() {

          pomelo.request("gate.gateHandler.queryEntry", {uid: 1}, function(data) {

            console.log('dataMINH: ', data);
            const _host = data?.host ?? '';
            const _port = data?.port ?? '';
            pomelo.disconnect();

            pomelo.init({
              host: _host,
              port: _port,
              log: true
            }, function() {

              // call login: test, minh
              pomelo.request("connector.entryHandler.loginDevice", {deviceId: deviceIdName}, function(data) {
                console.log('data: ', data);
                const userId = data?.player?.user_id ?? '';
                console.log('- userId: ', userId);

                // Get list tables by zone = TS
                pomelo.request('game.tableHandler.getTables', {zone: 'TS'}, function(data) {
                  const tables = data?.tables?.tables ?? [];
                  console.log('getTables with zone = TS >> tables: ', tables);
                  if (tables.length > 0) {
                    const randomTable = tables[0];
                    console.log('Random table: ', randomTable);
                    
                    // join table
                    const tid = randomTable?.id ?? null;
                    const minBuyIn = randomTable?.minBuyIn ?? 0;
                    const maxBuyIn = randomTable?.maxBuyIn ?? 0;

                    const cmdJoinTable = 'game.tableHandler.joinTable';
                    pomelo.request(cmdJoinTable, {tid: tid}, function(data) {
                      console.log('joinTable >> data: ', data);

                      // add Friend
                      const cmdAddFriend = 'game.userHandler.addFriend';
                      pomelo.request(cmdAddFriend, {uid: userId, fid: fid}, function(data) {
                        console.log('addFriend >> data: ', data);
                      });

                    });

                  } else {
                    console.log('No tables available');
                  }
                });                
              });
            
            });

          });

        });
      } // end addFriend

      function acceptFriend(deviceIdName, fid) {
        console.log("start pomelo");
        pomelo.init({
          host: host,
          port: port,
          log: true
        }, function() {

          pomelo.request("gate.gateHandler.queryEntry", {uid: 1}, function(data) {

            console.log('dataMINH: ', data);
            const _host = data?.host ?? '';
            const _port = data?.port ?? '';
            pomelo.disconnect();

            pomelo.init({
              host: _host,
              port: _port,
              log: true
            }, function() {

              // call login: test, minh
              pomelo.request("connector.entryHandler.loginDevice", {deviceId: deviceIdName}, function(data) {
                console.log('data: ', data);
                const userId = data?.player?.user_id ?? '';
                console.log('- userId: ', userId);

                // Get list tables by zone = TS
                pomelo.request('game.tableHandler.getTables', {zone: 'TS'}, function(data) {
                  const tables = data?.tables?.tables ?? [];
                  console.log('getTables with zone = TS >> tables: ', tables);
                  if (tables.length > 0) {
                    const randomTable = tables[0];
                    console.log('Random table: ', randomTable);
                    
                    // join table
                    const tid = randomTable?.id ?? null;
                    const minBuyIn = randomTable?.minBuyIn ?? 0;
                    const maxBuyIn = randomTable?.maxBuyIn ?? 0;

                    const cmdJoinTable = 'game.tableHandler.joinTable';
                    pomelo.request(cmdJoinTable, {tid: tid}, function(data) {
                      console.log('joinTable >> data: ', data);
                      // add Friend
                      const cmdAddFriend = 'game.userHandler.acceptFriend';
                      pomelo.request(cmdAddFriend, {fid: fid}, function(data) {
                        console.log('acceptFriend >> data: ', data);
                      });
                    });
                  } else {
                    console.log('No tables available');
                  }
                });                
              });
            
            });

          });

        });
      } // end acceptFriend

      function rejectedFriend(deviceIdName, fid) {
        console.log("start pomelo");
        pomelo.init({
          host: host,
          port: port,
          log: true
        }, function() {

          pomelo.request("gate.gateHandler.queryEntry", {uid: 1}, function(data) {

            console.log('dataMINH: ', data);
            const _host = data?.host ?? '';
            const _port = data?.port ?? '';
            pomelo.disconnect();

            pomelo.init({
              host: _host,
              port: _port,
              log: true
            }, function() {

              // call login: test, minh
              pomelo.request("connector.entryHandler.loginDevice", {deviceId: deviceIdName}, function(data) {
                console.log('data: ', data);
                const userId = data?.player?.user_id ?? '';
                console.log('- userId: ', userId);

                // Get list tables by zone = TS
                pomelo.request('game.tableHandler.getTables', {zone: 'TS'}, function(data) {
                  const tables = data?.tables?.tables ?? [];
                  console.log('getTables with zone = TS >> tables: ', tables);
                  if (tables.length > 0) {
                    const randomTable = tables[0];
                    console.log('Random table: ', randomTable);
                    
                    // join table
                    const tid = randomTable?.id ?? null;
                    const minBuyIn = randomTable?.minBuyIn ?? 0;
                    const maxBuyIn = randomTable?.maxBuyIn ?? 0;

                    const cmdJoinTable = 'game.tableHandler.joinTable';
                    pomelo.request(cmdJoinTable, {tid: tid}, function(data) {
                      console.log('joinTable >> data: ', data);
                      // add Friend
                      const cmdAddFriend = 'game.userHandler.rejectFriend';
                      pomelo.request(cmdAddFriend, {uid: userId, fid: fid}, function(data) {
                        console.log('rejectFriend >> data: ', data);
                      });
                    });
                  } else {
                    console.log('No tables available');
                  }
                });                
              });
            
            });

          });

        });
      } // end rejectedFriend

      function blockFriend(deviceIdName, fid) {
        console.log("start pomelo");
        pomelo.init({
          host: host,
          port: port,
          log: true
        }, function() {

          pomelo.request("gate.gateHandler.queryEntry", {uid: 1}, function(data) {

            console.log('dataMINH: ', data);
            const _host = data?.host ?? '';
            const _port = data?.port ?? '';
            pomelo.disconnect();

            pomelo.init({
              host: _host,
              port: _port,
              log: true
            }, function() {

              // call login: test, minh
              pomelo.request("connector.entryHandler.loginDevice", {deviceId: deviceIdName}, function(data) {
                console.log('data: ', data);
                const userId = data?.player?.user_id ?? '';
                console.log('- userId: ', userId);

                // Get list tables by zone = TS
                pomelo.request('game.tableHandler.getTables', {zone: 'TS'}, function(data) {
                  const tables = data?.tables?.tables ?? [];
                  console.log('getTables with zone = TS >> tables: ', tables);
                  if (tables.length > 0) {
                    const randomTable = tables[0];
                    console.log('Random table: ', randomTable);
                    
                    // join table
                    const tid = randomTable?.id ?? null;
                    const minBuyIn = randomTable?.minBuyIn ?? 0;
                    const maxBuyIn = randomTable?.maxBuyIn ?? 0;

                    const cmdJoinTable = 'game.tableHandler.joinTable';
                    pomelo.request(cmdJoinTable, {tid: tid}, function(data) {
                      console.log('joinTable >> data: ', data);
                      // add Friend
                      const cmdAddFriend = 'game.userHandler.blockFriend';
                      pomelo.request(cmdAddFriend, {uid: userId, fid: fid}, function(data) {
                        console.log('blockFriend >> data: ', data);
                      });
                    });
                  } else {
                    console.log('No tables available');
                  }
                });                
              });
            
            });

          });

        });
      } // end blockFriend

      function unBlockFriend(deviceIdName, fid) {
        console.log("start pomelo");
        pomelo.init({
          host: host,
          port: port,
          log: true
        }, function() {

          pomelo.request("gate.gateHandler.queryEntry", {uid: 1}, function(data) {

            console.log('dataMINH: ', data);
            const _host = data?.host ?? '';
            const _port = data?.port ?? '';
            pomelo.disconnect();

            pomelo.init({
              host: _host,
              port: _port,
              log: true
            }, function() {

              // call login: test, minh
              pomelo.request("connector.entryHandler.loginDevice", {deviceId: deviceIdName}, function(data) {
                console.log('data: ', data);
                const userId = data?.player?.user_id ?? '';
                console.log('- userId: ', userId);

                // Get list tables by zone = TS
                pomelo.request('game.tableHandler.getTables', {zone: 'TS'}, function(data) {
                  const tables = data?.tables?.tables ?? [];
                  console.log('getTables with zone = TS >> tables: ', tables);
                  if (tables.length > 0) {
                    const randomTable = tables[0];
                    console.log('Random table: ', randomTable);
                    
                    // join table
                    const tid = randomTable?.id ?? null;
                    const minBuyIn = randomTable?.minBuyIn ?? 0;
                    const maxBuyIn = randomTable?.maxBuyIn ?? 0;

                    const cmdJoinTable = 'game.tableHandler.joinTable';
                    pomelo.request(cmdJoinTable, {tid: tid}, function(data) {
                      console.log('joinTable >> data: ', data);
                      // add Friend
                      const cmdAddFriend = 'game.userHandler.unBlockFriend';
                      pomelo.request(cmdAddFriend, {uid: userId, fid: fid}, function(data) {
                        console.log('unBlockFriend >> data: ', data);
                      });
                    });
                  } else {
                    console.log('No tables available');
                  }
                });                
              });
            
            });

          });

        });
      } // end unBlockFriend

      function unFriend(deviceIdName, fid) {
        console.log("start pomelo");
        pomelo.init({
          host: host,
          port: port,
          log: true
        }, function() {

          pomelo.request("gate.gateHandler.queryEntry", {uid: 1}, function(data) {

            console.log('dataMINH: ', data);
            const _host = data?.host ?? '';
            const _port = data?.port ?? '';
            pomelo.disconnect();

            pomelo.init({
              host: _host,
              port: _port,
              log: true
            }, function() {

              // call login: test, minh
              pomelo.request("connector.entryHandler.loginDevice", {deviceId: deviceIdName}, function(data) {
                console.log('data: ', data);
                const userId = data?.player?.user_id ?? '';
                console.log('- userId: ', userId);

                // Get list tables by zone = TS
                pomelo.request('game.tableHandler.getTables', {zone: 'TS'}, function(data) {
                  const tables = data?.tables?.tables ?? [];
                  console.log('getTables with zone = TS >> tables: ', tables);
                  if (tables.length > 0) {
                    const randomTable = tables[0];
                    console.log('Random table: ', randomTable);
                    
                    // join table
                    const tid = randomTable?.id ?? null;
                    const minBuyIn = randomTable?.minBuyIn ?? 0;
                    const maxBuyIn = randomTable?.maxBuyIn ?? 0;

                    const cmdJoinTable = 'game.tableHandler.joinTable';
                    pomelo.request(cmdJoinTable, {tid: tid}, function(data) {
                      console.log('joinTable >> data: ', data);
                      // add Friend
                      const cmdAddFriend = 'game.userHandler.unFriend';
                      pomelo.request(cmdAddFriend, {fid: fid}, function(data) {
                        console.log('unFriend >> data: ', data);
                      });
                    });
                  } else {
                    console.log('No tables available');
                  }
                });                
              });
            
            });

          });

        });
      } // end unFriend

      function getFriends(deviceIdName, type) {
        console.log("start pomelo");
        pomelo.init({
          host: host,
          port: port,
          log: true
        }, function() {

          pomelo.request("gate.gateHandler.queryEntry", {uid: 1}, function(data) {

            console.log('dataMINH: ', data);
            const _host = data?.host ?? '';
            const _port = data?.port ?? '';
            pomelo.disconnect();

            pomelo.init({
              host: _host,
              port: _port,
              log: true
            }, function() {

              // call login: test, minh
              pomelo.request("connector.entryHandler.loginDevice", {deviceId: deviceIdName}, function(data) {
                console.log('data: ', data);
                const userId = data?.player?.user_id ?? '';
                console.log('- userId: ', userId);

                // Get list tables by zone = TS
                pomelo.request('game.tableHandler.getTables', {zone: 'TS'}, function(data) {
                  const tables = data?.tables?.tables ?? [];
                  console.log('getTables with zone = TS >> tables: ', tables);
                  if (tables.length > 0) {
                    const randomTable = tables[0];
                    console.log('Random table: ', randomTable);
                    
                    // join table
                    const tid = randomTable?.id ?? null;
                    const minBuyIn = randomTable?.minBuyIn ?? 0;
                    const maxBuyIn = randomTable?.maxBuyIn ?? 0;

                    const cmdJoinTable = 'game.tableHandler.joinTable';
                    pomelo.request(cmdJoinTable, {tid: tid}, function(data) {
                      console.log('joinTable >> data: ', data);
                      // get Friend
                      const cmdGetFriend = 'game.userHandler.getFriends';
                      pomelo.request(cmdGetFriend, {type: type, page: 1, limit: 5}, function(data) {
                        console.log('getFriends by type: ' + type + ' >> data: ', data);
                      });
                    });
                  } else {
                    console.log('No data available');
                  }
                });                
              });
            
            });

          });

        });
      } // end getFriends

      function searchFriends(deviceIdName, keyword, type) {
        console.log("start pomelo");
        pomelo.init({
          host: host,
          port: port,
          log: true
        }, function() {

          pomelo.request("gate.gateHandler.queryEntry", {uid: 1}, function(data) {

            console.log('dataMINH: ', data);
            const _host = data?.host ?? '';
            const _port = data?.port ?? '';
            pomelo.disconnect();

            pomelo.init({
              host: _host,
              port: _port,
              log: true
            }, function() {

              // call login: test, minh
              pomelo.request("connector.entryHandler.loginDevice", {deviceId: deviceIdName}, function(data) {
                console.log('data: ', data);
                const userId = data?.player?.user_id ?? '';
                console.log('- userId: ', userId);

                // Get list tables by zone = TS
                pomelo.request('game.tableHandler.getTables', {zone: 'TS'}, function(data) {
                  const tables = data?.tables?.tables ?? [];
                  console.log('getTables with zone = TS >> tables: ', tables);
                  if (tables.length > 0) {
                    const randomTable = tables[0];
                    console.log('Random table: ', randomTable);
                    
                    // join table
                    const tid = randomTable?.id ?? null;
                    const minBuyIn = randomTable?.minBuyIn ?? 0;
                    const maxBuyIn = randomTable?.maxBuyIn ?? 0;

                    const cmdJoinTable = 'game.tableHandler.joinTable';
                    pomelo.request(cmdJoinTable, {tid: tid}, function(data) {
                      console.log('joinTable >> data: ', data);
                      // search Friend
                      const cmdSearchFriend = 'game.userHandler.searchFriends';
                      pomelo.request(cmdSearchFriend, {keyword: keyword, type: type, page: 1, limit: 5}, function(data) {
                        console.log('searchFriends by type: ' + type + ' >> data: ', data);
                      });
                    });
                  } else {
                    console.log('No data available');
                  }
                });                
              });
            
            });

          });

        });
      } // end searchFriends

      function getQuests(deviceIdName) {
        console.log("start pomelo");
        pomelo.init({
          host: host,
          port: port,
          log: true
        }, function() {

          pomelo.request("gate.gateHandler.queryEntry", {uid: 1}, function(data) {

            console.log('dataMINH: ', data);
            const _host = data?.host ?? '';
            const _port = data?.port ?? '';
            pomelo.disconnect();

            pomelo.init({
              host: _host,
              port: _port,
              log: true
            }, function() {

              // call login: test, minh
              pomelo.request("connector.entryHandler.loginDevice", {deviceId: deviceIdName}, function(data) {
                console.log('data: ', data);
                const userId = data?.player?.user_id ?? '';
                console.log('- userId: ', userId);

                // Get list tables by zone = TS
                pomelo.request('game.tableHandler.getTables', {zone: 'TS'}, function(data) {
                  const tables = data?.tables?.tables ?? [];
                  console.log('getTables with zone = TS >> tables: ', tables);
                  if (tables.length > 0) {
                    const randomTable = tables[0];
                    console.log('Random table: ', randomTable);
                    
                    // join table
                    const tid = randomTable?.id ?? null;
                    const minBuyIn = randomTable?.minBuyIn ?? 0;
                    const maxBuyIn = randomTable?.maxBuyIn ?? 0;

                    const cmdGetQuests = 'game.questHandler.getQuests';
                    pomelo.request(cmdGetQuests, {page: 1, limit: 10}, function(data) {
                      console.log('getQuests >> data: ', data);
                    });
                  } else {
                    console.log('No data available');
                  }
                });                
              });
            
            });

          });

        });
      } // end getQuests

      function claimQuestReward(deviceIdName) {
        console.log("start pomelo");
        pomelo.init({
          host: host,
          port: port,
          log: true
        }, function() {

          pomelo.request("gate.gateHandler.queryEntry", {uid: 1}, function(data) {

            console.log('dataMINH: ', data);
            const _host = data?.host ?? '';
            const _port = data?.port ?? '';
            pomelo.disconnect();

            pomelo.init({
              host: _host,
              port: _port,
              log: true
            }, function() {

              // call login: test, minh
              pomelo.request("connector.entryHandler.loginDevice", {deviceId: deviceIdName}, function(data) {
                console.log('data: ', data);
                const userId = data?.player?.user_id ?? '';
                console.log('- userId: ', userId);

                // Get list tables by zone = TS
                pomelo.request('game.tableHandler.getTables', {zone: 'TS'}, function(data) {
                  const tables = data?.tables?.tables ?? [];
                  console.log('getTables with zone = TS >> tables: ', tables);
                  if (tables.length > 0) {
                    const randomTable = tables[0];
                    console.log('Random table: ', randomTable);
                    
                    // join table
                    const tid = randomTable?.id ?? null;
                    const minBuyIn = randomTable?.minBuyIn ?? 0;
                    const maxBuyIn = randomTable?.maxBuyIn ?? 0;

                    const cmdGetQuests = 'game.questHandler.claimQuestReward';
                    pomelo.request(cmdGetQuests, {questId: 1}, function(data) {
                      console.log('getQuests >> data: ', data);
                    });
                  } else {
                    console.log('No data available');
                  }
                });                
              });
            
            });

          });

        });
      } // end claimQuestReward

      function getRandomUsers(deviceIdName, limit) {
        console.log("start pomelo");
        pomelo.init({
          host: host,
          port: port,
          log: true
        }, function() {

          pomelo.request("gate.gateHandler.queryEntry", {uid: 1}, function(data) {

            console.log('dataMINH: ', data);
            const _host = data?.host ?? '';
            const _port = data?.port ?? '';
            pomelo.disconnect();

            pomelo.init({
              host: _host,
              port: _port,
              log: true
            }, function() {

              // call login: test, minh
              pomelo.request("connector.entryHandler.loginDevice", {deviceId: deviceIdName}, function(data) {
                console.log('data: ', data);
                const userId = data?.player?.user_id ?? '';
                console.log('- userId: ', userId);

                // Get list tables by zone = TS
                pomelo.request('game.tableHandler.getTables', {zone: 'TS'}, function(data) {
                  const tables = data?.tables?.tables ?? [];
                  console.log('getTables with zone = TS >> tables: ', tables);
                  if (tables.length > 0) {
                    const randomTable = tables[0];
                    console.log('Random table: ', randomTable);
                    
                    // join table
                    const tid = randomTable?.id ?? null;
                    const minBuyIn = randomTable?.minBuyIn ?? 0;
                    const maxBuyIn = randomTable?.maxBuyIn ?? 0;

                    const cmdJoinTable = 'game.tableHandler.joinTable';
                    pomelo.request(cmdJoinTable, {tid: tid}, function(data) {
                      console.log('joinTable >> data: ', data);
                      // add Friend
                      const cmdGetRandomUsers = 'game.userHandler.getRandomUsers';
                      pomelo.request(cmdGetRandomUsers, {limit: limit}, function(data) {
                        console.log('getRandomUsers with limit: ' + limit + ' >> data: ', data);
                      });
                    });
                  } else {
                    console.log('No tables available');
                  }
                });                
              });
            
            });

          });

        });
      } // end getRandomUsers

      function quickJoinTable(deviceIdName) {
        console.log("start pomelo");
        pomelo.init({
          host: host,
          port: port,
          log: true
        }, function() {

          pomelo.request("gate.gateHandler.queryEntry", {uid: 1}, function(data) {

            console.log('dataMINH: ', data);
            const _host = data?.host ?? '';
            const _port = data?.port ?? '';
            pomelo.disconnect();

            pomelo.init({
              host: _host,
              port: _port,
              log: true
            }, function() {

              // call login: test, minh
              pomelo.request("connector.entryHandler.loginDevice", {deviceId: deviceIdName}, function(data) {
                console.log('data: ', data);
                const userId = data?.player?.user_id ?? '';
                console.log('- userId: ', userId);

                // quick join table
                pomelo.request('game.tableHandler.quickJoinTable', '', function(data) {
                  // const tables = data?.tables?.tables ?? [];
                  console.log('quickJoinTable >> data: ', data);
                });                
              });
            
            });

          });

        });
      } // end quickJoinTable

      function getMessagesAndEmail(deviceIdName) {
        console.log("start pomelo");
        pomelo.init({
          host: host,
          port: port,
          log: true
        }, function() {

          pomelo.request("gate.gateHandler.queryEntry", {uid: 1}, function(data) {

            console.log('dataMINH: ', data);
            const _host = data?.host ?? '';
            const _port = data?.port ?? '';
            pomelo.disconnect();

            pomelo.init({
              host: _host,
              port: _port,
              log: true
            }, function() {

              // call login: test, minh
              pomelo.request("connector.entryHandler.loginDevice", {deviceId: deviceIdName}, function(data) {
                console.log('data: ', data);
                const userId = data?.player?.user_id ?? '';
                console.log('- userId: ', userId);

                // quick join table
                pomelo.request('game.userHandler.getMessagesAndEmails', '', function(data) {
                  // const tables = data?.tables?.tables ?? [];
                  console.log('getMessagesAndEmails >> data: ', data);
                });                
              });
            
            });

          });

        });
      } // end getMessagesAndEmail

      function loginAndGetUserInfo(deviceIdName) {
        console.log("start pomelo");
        pomelo.init({
          host: host,
          port: port,
          log: true
        }, function() {

          pomelo.request("gate.gateHandler.queryEntry", {uid: 1}, function(data) {

            console.log('dataMINH: ', data);
            const _host = data?.host ?? '';
            const _port = data?.port ?? '';
            pomelo.disconnect();

            pomelo.init({
              host: _host,
              port: _port,
              log: true
            }, function() {

              // call login: test, minh
              pomelo.request("connector.entryHandler.loginDevice", {deviceId: deviceIdName}, function(data) {
                console.log('data player: ', data);
                // const userId = data?.player?.user_id ?? '';
                const userId = data?.player?.id ?? 0;
                console.log('- userId: ', userId);

                // Create table
                // ----------------------------------------------------------------
                // const createTable = 'game.tableHandler.createTable';
                // pomelo.request(createTable, dataPost, function(data) {
                  // console.log('createTable >> data: ', data);
                // });

                // Command Get Thông tin 1 user
                // =================================================================
                
                const cmdGetUserInfo = 'game.userHandler.getUserInfo';
                pomelo.request(cmdGetUserInfo, {uid: userId}, function(data) {
                  console.log('- getUserInfo >> data: ', data);
                });
                
              });
            
            });

          });

        });
      } // end player

      function getMiniGameInfo(deviceIdName) {
        console.log("start getMiniGameInfo");
        pomelo.init({
          host: host,
          port: port,
          log: true
        }, function() {
          
          pomelo.request("gate.gateHandler.queryEntry", {uid: 1}, function(data) {
            console.log('data gate: ', data);
            const _host = data?.host ?? '';
            const _port = data?.port ?? '';
            pomelo.disconnect();

            pomelo.init({
              host: _host,
              port: _port,
              log: true
            }, function() {
              pomelo.request("connector.entryHandler.loginDevice", {deviceId: deviceIdName}, function(data) {
                console.log('data after login: ', data);
                const userId = data?.player?.user_id ?? '';
                console.log('- userId: ', userId);
    
                // get thong tin luật chơi và cấu hình mini game
                const cmdGetMiniGameInfo = 'game.minigameHandler.getGameInfo';
                pomelo.request(cmdGetMiniGameInfo, '', function(data) {
                  console.log('getMiniGameInfo >> data: ', data);
                });          
    
              });
            });
          });
        });

        
      } // end getMiniGameInfo

      function miniGameGetLuckyCard(deviceIdName) {
        console.log("start miniGameGetLuckyCard");
        pomelo.init({
          host: host,
          port: port,
          log: true
        }, function() {
          
          pomelo.request("gate.gateHandler.queryEntry", {uid: 1}, function(data) {
            console.log('data gate: ', data);
            const _host = data?.host ?? '';
            const _port = data?.port ?? '';
            pomelo.disconnect();

            pomelo.init({
              host: _host,
              port: _port,
              log: true
            }, function() {
              pomelo.request("connector.entryHandler.loginDevice", {deviceId: deviceIdName}, function(data) {
                console.log('data after login: ', data);
                const userId = data?.player?.user_id ?? '';
                console.log('- userId: ', userId);
    
                // get thong tin bài may mắn ngày hôm nay
                const cmdLuckyCard = 'game.minigameHandler.getLuckyCard';
                pomelo.request(cmdLuckyCard, '', function(data) {
                  console.log('getLuckyCard >> data: ', data);
                });           
    
              });
            });
          });
        });
      } // end miniGameGetLuckyCard

      function spinMiniGame(deviceIdName) {
        console.log("start spinMiniGame");
        pomelo.init({
          host: host,
          port: port,
          log: true
        }, function() {
          
          pomelo.request("gate.gateHandler.queryEntry", {uid: 1}, function(data) {
            console.log('data gate: ', data);
            const _host = data?.host ?? '';
            const _port = data?.port ?? '';
            pomelo.disconnect();

            pomelo.init({
              host: _host,
              port: _port,
              log: true
            }, function() {
              pomelo.request("connector.entryHandler.loginDevice", {deviceId: deviceIdName}, function(data) {
                console.log('data after login: ', data);
                const userId = data?.player?.user_id ?? '';
                console.log('- userId: ', userId);
    
                // spin mini game
                const cmdSpin = 'game.minigameHandler.spin';
                pomelo.request(cmdSpin, {betAmount: 1000000}, function(data) {
                  console.log('spin >> data: ', data);
                });           
    
              });
            });
          });
        });
      } // end spinMiniGame

      function getSpinHistory(deviceIdName) {
        console.log("start spinMiniGame");
        pomelo.init({
          host: host,
          port: port,
          log: true
        }, function() {
          
          pomelo.request("gate.gateHandler.queryEntry", {uid: 1}, function(data) {
            console.log('data gate: ', data);
            const _host = data?.host ?? '';
            const _port = data?.port ?? '';
            pomelo.disconnect();

            pomelo.init({
              host: _host,
              port: _port,
              log: true
            }, function() {
              pomelo.request("connector.entryHandler.loginDevice", {deviceId: deviceIdName}, function(data) {
                console.log('data after login: ', data);
                const userId = data?.player?.user_id ?? '';
                console.log('- userId: ', userId);
    
                // lấy lịch sử quay thưởng
                const cmdGetSpinHistory = 'game.minigameHandler.getSpinHistory';
                pomelo.request(cmdGetSpinHistory, '', function(data) {
                  console.log('getSpinHistory >> data: ', data);
                });           
    
              });
            });
          });
        });
      } // end getSpinHistory - lịch sử quay thưởng

      function toggleAutoSpin(deviceIdName) {
        console.log("start spinMiniGame");
        pomelo.init({
          host: host,
          port: port,
          log: true
        }, function() {
          
          pomelo.request("gate.gateHandler.queryEntry", {uid: 1}, function(data) {
            console.log('data gate: ', data);
            const _host = data?.host ?? '';
            const _port = data?.port ?? '';
            pomelo.disconnect();

            pomelo.init({
              host: _host,
              port: _port,
              log: true
            }, function() {
              pomelo.request("connector.entryHandler.loginDevice", {deviceId: deviceIdName}, function(data) {
                console.log('data after login: ', data);
                const userId = data?.player?.user_id ?? '';
                console.log('- userId: ', userId);
    
                // bật tắt chế độ tự động quay
                const cmdToggleAutoSpin = 'game.minigameHandler.toggleAutoSpin';
                pomelo.request(cmdToggleAutoSpin, {autoSpin: true, betAmount: 1000000}, function(data) {
                  console.log('toggleAutoSpin >> data: ', data);
                });           
    
              });
            });
          });
        });
      } // end toggleAutoSpin - bật tắt chế độ tự động quay
      
      function chat1to1(deviceIdName, uidFriend) {
        console.log("start pomelo");
        pomelo.init({
          host: host,
          port: port,
          log: true
        }, function() {

          pomelo.request("gate.gateHandler.queryEntry", {uid: 1}, function(data) {

            console.log('chat1to1 >> data: ', data);
            const _host = data?.host ?? '';
            const _port = data?.port ?? '';
            pomelo.disconnect();

            pomelo.init({
              host: _host,
              port: _port,
              log: true
            }, function() {

              // call login: test, minh
              pomelo.request("connector.entryHandler.loginDevice", {deviceId: deviceIdName}, function(data) {
                console.log(deviceIdName, ' -> data player: ', data);
                const userId = data?.player?.user_id ?? '';
                console.log('- userId: ', userId);

                // Sleep 3s sau đó tạo ra ngẫu nhiên 1 đoạn nội dung chat và tiến hành gửi tin nhắn đi, lặp qua việc này 10 lần
                for (let i = 0; i < 10; i++) {
                  setTimeout(() => {
                    const messages = [
                      'Hi there!',
                      'How are you doing?', 
                      'Nice to meet you!',
                      'What\'s up?',
                      'Having a good day?',
                      'Hello friend!',
                      'Hey, how\'s it going?',
                      'Greetings!',
                      'Good to see you!',
                      'Hi, hope you\'re well!'
                    ];
                    const content = messages[Math.floor(Math.random() * messages.length)];
                    const cmdChat1to1 = 'chat.chatHandler.sendMessage';
                    pomelo.request(cmdChat1to1, {target: uidFriend, content: content}, function(data) {
                      console.log('chat1to1 ' + i + ' >> data: ', data);
                    });
                  }, 3000);
                }
                
                
                // send message to deviceIdName2
                /* const cmdChat1to1 = 'chat.chatHandler.sendMessage';
                pomelo.request(cmdChat1to1, {target: uidFriend, content: 'Hello, how are you?'}, function(data) {
                  console.log('chat1to1 >> data: ', data);
                }); */

                
              });
            
            });

          });

        });
      } // end chat1to1

      function getChatSessions(deviceIdName, uid) {
        console.log("start pomelo");
        pomelo.init({
          host: host,
          port: port,
          log: true
        }, function() {

          pomelo.request("gate.gateHandler.queryEntry", {uid: 1}, function(data) {

            console.log('chat1to1 >> data: ', data);
            const _host = data?.host ?? '';
            const _port = data?.port ?? '';
            pomelo.disconnect();

            pomelo.init({
              host: _host,
              port: _port,
              log: true
            }, function() {

              // call login: test, minh
              pomelo.request("connector.entryHandler.loginDevice", {deviceId: deviceIdName}, function(data) {
                console.log(deviceIdName, ' -> data player: ', data);
                const userId = data?.player?.user_id ?? '';
                console.log('- userId: ', userId);

                const cmdGetChatSessions = 'chat.chatHandler.getChatSessions';
                pomelo.request(cmdGetChatSessions, {uid: uid}, function(data) {
                  console.log('getChatSessions >> data: ', data);
                });
                
              });
            
            });

          });

        });
      } // end getChatSessions

      function getChatHistories(deviceIdName, uid) {
        console.log("start pomelo");
        pomelo.init({
          host: host,
          port: port,
          log: true
        }, function() {

          pomelo.request("gate.gateHandler.queryEntry", {uid: 1}, function(data) {

            console.log('chat1to1 >> data: ', data);
            const _host = data?.host ?? '';
            const _port = data?.port ?? '';
            pomelo.disconnect();

            pomelo.init({
              host: _host,
              port: _port,
              log: true
            }, function() {

              // call login: test, minh
              pomelo.request("connector.entryHandler.loginDevice", {deviceId: deviceIdName}, function(data) {
                console.log(deviceIdName, ' -> data player: ', data);
                const userId = data?.player?.user_id ?? '';
                console.log('- userId: ', userId);

                const cmdGetChatHistories = 'chat.chatHandler.getChatHistories';
                pomelo.request(cmdGetChatHistories, {target_id: uid, page: 1, pageSize: 20}, function(data) {
                  console.log('getChatHistories >> data: ', data);
                });
                
              });
            
            });

          });

        });
      } // end getChatHistories

      function getUnreadMessageCount(deviceIdName, uid) {
        console.log("start pomelo");
        pomelo.init({
          host: host,
          port: port,
          log: true
        }, function() {

          pomelo.request("gate.gateHandler.queryEntry", {uid: 1}, function(data) {

            console.log('chat1to1 >> data: ', data);
            const _host = data?.host ?? '';
            const _port = data?.port ?? '';
            pomelo.disconnect();

            pomelo.init({
              host: _host,
              port: _port,
              log: true
            }, function() {

              // call login: test, minh
              pomelo.request("connector.entryHandler.loginDevice", {deviceId: deviceIdName}, function(data) {
                console.log(deviceIdName, ' -> data player: ', data);
                const userId = data?.player?.user_id ?? '';
                console.log('- userId: ', userId);

                const cmdGetUnreadMessageCount = 'chat.chatHandler.getUnreadMessageCount';
                pomelo.request(cmdGetUnreadMessageCount, {uid: uid}, function(data) {
                  console.log('getUnreadMessageCount >> data: ', data);
                });
                
              });
            
            });

          });

        });
      } // end getUnreadMessageCount

      function markAllMessagesAsRead(deviceIdName, uid) {
        console.log("start pomelo");
        pomelo.init({
          host: host,
          port: port,
          log: true
        }, function() {

          pomelo.request("gate.gateHandler.queryEntry", {uid: 1}, function(data) {

            console.log('chat1to1 >> data: ', data);
            const _host = data?.host ?? '';
            const _port = data?.port ?? '';
            pomelo.disconnect();

            pomelo.init({
              host: _host,
              port: _port,
              log: true
            }, function() {

              // call login: test, minh
              pomelo.request("connector.entryHandler.loginDevice", {deviceId: deviceIdName}, function(data) {
                console.log(deviceIdName, ' -> data player: ', data);
                const userId = data?.player?.user_id ?? '';
                console.log('- userId: ', userId);

                const cmdGetUnreadMessageCount = 'chat.chatHandler.markAllMessagesAsRead';
                pomelo.request(cmdGetUnreadMessageCount, {friend_id: 19}, function(data) {
                  console.log('getUnreadMessageCount >> data: ', data);
                });
                
              });
            
            });

          });

        });
      } // end markAllMessagesAsRead

      function deleteMessage(deviceIdName, uid, message_id) {
        console.log("start pomelo");
        pomelo.init({
          host: host,
          port: port,
          log: true
        }, function() {

          pomelo.request("gate.gateHandler.queryEntry", {uid: 1}, function(data) {

            console.log('chat1to1 >> data: ', data);
            const _host = data?.host ?? '';
            const _port = data?.port ?? '';
            pomelo.disconnect();

            pomelo.init({
              host: _host,
              port: _port,
              log: true
            }, function() {

              // call login: test, minh
              pomelo.request("connector.entryHandler.loginDevice", {deviceId: deviceIdName}, function(data) {
                console.log(deviceIdName, ' -> data player: ', data);
                const userId = data?.player?.user_id ?? '';
                console.log('- userId: ', userId);

                const cmdDeleteMessage = 'chat.chatHandler.deleteMessage';
                pomelo.request(cmdDeleteMessage, {message_id: message_id}, function(data) {
                  console.log('deleteMessage >> data: ', data);
                });
                
              });
            
            });

          });

        });
      } // end deleteMessage

      function getAllBadges(deviceName = 'minh5') {
        console.log("getAllBadges: ", deviceName);
        pomelo.init({
          host: host,
          port: port,
          log: true
        }, function() {
          pomelo.request("gate.gateHandler.queryEntry", {uid: 1}, function(data) {
            const _host = data?.host ?? '';
            const _port = data?.port ?? '';
            pomelo.disconnect();
            
            pomelo.init({
              host: _host,
              port: _port,
              log: true
            }, function() {
              // Login first
              pomelo.request("connector.entryHandler.loginDevice", {deviceId: deviceName}, function(data) {
                console.log('Login data:', data);
                
                // Send notification to all tables in zone TS
                const dataPost = {
                  // type: "POKE_CAREER" // POKE_CAREER, ACHIEVEMENTS
                  type: ["POKE_CAREER", "ACHIEVEMENTS"]
                };
                
                pomelo.request('game.userHandler.getAllBadges', dataPost, function(response) {
                  console.log('getAllBadges response:', response);
                });
              });
            });
          });
        });
      } // end getAllBadges

      function claimBadgeReward(deviceName = 'minh5', badge_id = 6) {
        console.log("getAllBadges: ", deviceName);
        pomelo.init({
          host: host,
          port: port,
          log: true
        }, function() {
          pomelo.request("gate.gateHandler.queryEntry", {uid: 1}, function(data) {
            const _host = data?.host ?? '';
            const _port = data?.port ?? '';
            pomelo.disconnect();
            
            pomelo.init({
              host: _host,
              port: _port,
              log: true
            }, function() {
              // Login first
              pomelo.request("connector.entryHandler.loginDevice", {deviceId: deviceName}, function(data) {
                console.log('Login data:', data);
                
                // Send notification to all tables in zone TS
                const dataPost = {
                  badge_id: badge_id,
                };
                
                pomelo.request('game.userHandler.claimBadgeReward', dataPost, function(response) {
                  console.log('getAllBadges response:', response);
                });
              });
            });
          });
        });
      } // end getAllBadges

      function getShopItems(deviceName = 'minh5') {
        console.log("getShopItems: ", deviceName);
        pomelo.init({
          host: host,
          port: port,
          log: true
        }, function() {
          pomelo.request("gate.gateHandler.queryEntry", {uid: 1}, function(data) {
            const _host = data?.host ?? '';
            const _port = data?.port ?? '';
            pomelo.disconnect();
            
            pomelo.init({
              host: _host,
              port: _port,
              log: true
            }, function() {
              // Login first
              pomelo.request("connector.entryHandler.loginDevice", {deviceId: deviceName}, function(data) {
                console.log('Login data:', data);
                
                const dataPost = {
                  grouped: true
                };
                
                pomelo.request('game.shopHandler.getShopItems', dataPost, function(response) {
                  console.log('getShopItems >> response:', response);
                });
              });
            });
          });
        });
      } // end getShopItems

      function getShopItemDetail(deviceName = 'minh5', itemId) {
        console.log("getShopItemDetail: ", deviceName);
        pomelo.init({
          host: host,
          port: port,
          log: true
        }, function() {
          pomelo.request("gate.gateHandler.queryEntry", {uid: 1}, function(data) {
            const _host = data?.host ?? '';
            const _port = data?.port ?? '';
            pomelo.disconnect();
            
            pomelo.init({
              host: _host,
              port: _port,
              log: true
            }, function() {
              // Login first
              pomelo.request("connector.entryHandler.loginDevice", {deviceId: deviceName}, function(data) {
                console.log('Login data:', data);
                
                const dataPost = {
                  item_id: itemId,
                };
                
                pomelo.request('game.shopHandler.getShopItemDetail', dataPost, function(response) {
                  console.log('getShopItemDetail >> response:', response);
                });
              });
            });
          });
        });
      } // end getShopItemDetail

      function buyItem(deviceName = 'minh5', itemId) {
        console.log("buyItem: ", deviceName);
        pomelo.init({
          host: host,
          port: port,
          log: true
        }, function() {
          pomelo.request("gate.gateHandler.queryEntry", {uid: 1}, function(data) {
            const _host = data?.host ?? '';
            const _port = data?.port ?? '';
            pomelo.disconnect();
            
            pomelo.init({
              host: _host,
              port: _port,
              log: true
            }, function() {
              // Login first
              pomelo.request("connector.entryHandler.loginDevice", {deviceId: deviceName}, function(data) {
                console.log('Login data:', data);
                
                // Send notification to all tables in zone TS
                const dataPost = {
                  item_id: 1,
                  platform: "web"
                };
                
                pomelo.request('game.shopHandler.buyItem', dataPost, function(response) {
                  console.log('buyItem >> response:', response);
                });
              });
            });
          });
        });
      } // end buyItem

      function getPurchaseHistory(deviceName = 'minh5') {
        console.log("getPurchaseHistory: ", deviceName);
        pomelo.init({
          host: host,
          port: port,
          log: true
        }, function() {
          pomelo.request("gate.gateHandler.queryEntry", {uid: 1}, function(data) {
            const _host = data?.host ?? '';
            const _port = data?.port ?? '';
            pomelo.disconnect();
            
            pomelo.init({
              host: _host,
              port: _port,
              log: true
            }, function() {
              // Login first
              pomelo.request("connector.entryHandler.loginDevice", {deviceId: deviceName}, function(data) {
                console.log('Login data:', data);
                
                pomelo.request('game.shopHandler.getPurchaseHistory', {}, function(response) {
                  console.log('getPurchaseHistory >> response:', response);
                });
              });
            });
          });
        });
      } // end getPurchaseHistory

      function getMissions(deviceName = 'minh5') {
        console.log("getShopItems: ", deviceName);
        pomelo.init({
          host: host,
          port: port,
          log: true
        }, function() {
          pomelo.request("gate.gateHandler.queryEntry", {uid: 1}, function(data) {
            const _host = data?.host ?? '';
            const _port = data?.port ?? '';
            pomelo.disconnect();
            
            pomelo.init({
              host: _host,
              port: _port,
              log: true
            }, function() {
              // Login first
              pomelo.request("connector.entryHandler.loginDevice", {deviceId: deviceName}, function(data) {
                console.log('Login data:', data);
                
                pomelo.request('game.missionsHandler.getMissions', {}, function(response) {
                  console.log('getMissions >> response:', response);
                });
              });
            });
          });
        });
      } // end getMissions

      function claimRewardMission(deviceName = 'minh5', missionId) {
        console.log("claimRewardMission: ", deviceName);
        pomelo.init({
          host: host,
          port: port,
          log: true
        }, function() {
          pomelo.request("gate.gateHandler.queryEntry", {uid: 1}, function(data) {
            const _host = data?.host ?? '';
            const _port = data?.port ?? '';
            pomelo.disconnect();
            
            pomelo.init({
              host: _host,
              port: _port,
              log: true
            }, function() {
              // Login first
              pomelo.request("connector.entryHandler.loginDevice", {deviceId: deviceName}, function(data) {
                console.log('Login data:', data);
                
                pomelo.request('game.missionsHandler.claimReward', {mission_id: missionId}, function(response) {
                  console.log('claimReward >> response:', response);
                });
              });
            });
          });
        });
      } // end claimRewardMission

      function getLeaderboards(deviceName = 'minh5') {
        console.log("getShopItems: ", deviceName);
        pomelo.init({
          host: host,
          port: port,
          log: true
        }, function() {
          pomelo.request("gate.gateHandler.queryEntry", {uid: 1}, function(data) {
            const _host = data?.host ?? '';
            const _port = data?.port ?? '';
            pomelo.disconnect();
            
            pomelo.init({
              host: _host,
              port: _port,
              log: true
            }, function() {
              // Login first
              pomelo.request("connector.entryHandler.loginDevice", {deviceId: deviceName}, function(data) {
                console.log('Login data:', data);
                
                pomelo.request('game.leaderboardHandler.getLeaderboards', {type: 'CHIPS'}, function(response) {
                  console.log('getLeaderboards >> response:', response);
                });
              });
            });
          });
        });
      } // end getLeaderboards

      function getPlayerRanking(deviceName = 'minh5') {
        console.log("getShopItems: ", deviceName);
        pomelo.init({
          host: host,
          port: port,
          log: true
        }, function() {
          pomelo.request("gate.gateHandler.queryEntry", {uid: 1}, function(data) {
            const _host = data?.host ?? '';
            const _port = data?.port ?? '';
            pomelo.disconnect();
            
            pomelo.init({
              host: _host,
              port: _port,
              log: true
            }, function() {
              // Login first
              pomelo.request("connector.entryHandler.loginDevice", {deviceId: deviceName}, function(data) {
                console.log('Login data:', data);
                
                pomelo.request('game.leaderboardHandler.getPlayerRanking', {type: 'CHIPS'}, function(response) {
                  console.log('getPlayerRanking >> response:', response);
                });
              });
            });
          });
        });
      } // end getPlayerRanking

      pomelo.on("onInvitePlay", function (data) {
        console.log("[gameMessageEvent]->onInvitePlay: " + JSON.stringify(data));
      });

      pomelo.on("onChat", function (data) {
        console.log("[gameMessageEvent]->onChat: " + JSON.stringify(data));
      });

      pomelo.on("onJoinTable", function (data) {
        console.log("[gameMessageEvent]->onJoinTable: " + JSON.stringify(data));
      });

      pomelo.on("onJoinGame", function (data) {
        console.log("[gameMessageEvent]->onJoinGame: " + JSON.stringify(data));
      });

      pomelo.on("onTableEvent", function (data) {
        console.log("[gameMessageEvent]->onTableEvent: " + JSON.stringify(data));
      });

      pomelo.on("onEndTurn", function (data) {
        console.log("[gameMessageEvent]->onEndTurn: " + JSON.stringify(data));
      });

      pomelo.on("onUpdateMyself", function (data) {
        console.log("[gameMessageEvent]->onUpdateMyself: " + JSON.stringify(data));
      });

      pomelo.on("onNotification", function (data) {
        console.log("[gameMessageEvent]->onNotification: " + JSON.stringify(data));
      });

      pomelo.on("onFriends", function (data) {
        console.log("[gameMessageEvent]->onFriends: " + JSON.stringify(data));
      });

      pomelo.on("onChat", function (data) {
        console.log("[onChat]->data: " + JSON.stringify(data));
      });

      pomelo.on("onUserChat", function (data) {
        console.log("[onUserChat]->data: " + JSON.stringify(data));
      });

    </script>
 
  </head>
  <br>

      <div class="g-banner" style="border:none">
      </div>
      <div class="g-button">
        <input id="test" type="button" value="Test Game Server (login device)" onclick="show('minh13')" />
      </div>

      <div class="g-button">
        <input id="test" type="button" value="Get User Info" onclick="loginAndGetUserInfo('minh5')" />
      </div>

      <div class="g-button">
        <input id="test" type="button" value="Login Into Lobby (minh4)" onclick="show2('minh4')" />
      </div>

      <div class="g-button">
        <input id="test" type="button" value="Login Into Lobby (minh14)" onclick="show2('minh14')" />
      </div>

      <div class="g-button">
        <input id="test" type="button" value="switchTable" onclick="switchTable()" />
      </div>

      <div class="g-button">
        <input id="test" type="button" value="findTable" onclick="findTable()" />
      </div>

      <hr />

      <div class="g-button">
        <input id="test" type="button" value="Chat In Table" onclick="chatInTable()" />
      </div>

      <div class="g-button">
        <input id="test" type="button" value="Chat 1 to 1 (minh5)" onclick="chat1to1('minh5', 19)" />
      </div>
      
      <div class="g-button">
        <input id="test" type="button" value="Chat 1 to 1 (minh4)" onclick="chat1to1('minh4', 5)" />
      </div>

      <div class="g-button">
        <input id="test" type="button" value="Get danh sách Chat của (minh5)" onclick="getChatSessions('minh5', 5)" />
      </div>

      <div class="g-button">
        <input id="test" type="button" value="Lịch sử chat của minh5 và minh4" onclick="getChatHistories('minh5', 19)" />
      </div>

      <div class="g-button">
        <input id="test" type="button" value="Đếm số tin nhắn chưa đọc theo từng sessions" onclick="getUnreadMessageCount('minh5', 5)" />
      </div>

      <div class="g-button">
        <input id="test" type="button" value="Đánh dấu tất cả tin nhắn đã đọc" onclick="markAllMessagesAsRead('minh5', 5)" />
      </div>

      <div class="g-button">
        <input id="test" type="button" value="Xoá tin nhắn của minh5 với message_id = 4" onclick="deleteMessage('minh5', 5, 4)" />
      </div>

      <hr />

      <div class="g-button">
        <input id="test" type="button" value="Player 1 (minh5)" onclick="playerInGame('minh5', 5)" />
      </div>

      <div class="g-button">
        <input id="test" type="button" value="Player 2 (minh)" onclick="playerInGame('minh', 2)" />
      </div>

      <div class="g-button">
        <input id="test" type="button" value="Player 3 (minh3) ngồi xuống, đứng dậy" onclick="playerInGame2('minh3', 4)" />
      </div>

      <div class="g-button">
        <input id="test" type="button" value="Login with Facebook Info" onclick="loginFacebook()" />
      </div>

      <div class="g-button">
        <input id="test" type="button" value="Login By Email" onclick="loginByEmail()" />
      </div>

      <hr />
      <div class="g-button">
        <input id="test" type="button" value="Gửi request kết bạn(minh5) to minh4 (19)" onclick="addFriend('minh5', 19)" />
      </div>

      <div class="g-button">
        <input id="test" type="button" value="Đồng ý yêu cầu kết bạn (minh4) with minh5" onclick="acceptFriend('minh4', 5)" />
      </div>

      <div class="g-button">
        <input id="test" type="button" value="Từ chối yêu cầu kết bạn" onclick="rejectedFriend('minh', 5)" />
      </div>

      <div class="g-button">
        <input id="test" type="button" value="Hủy bạn bè" onclick="unFriend('minh', 5)" />
      </div>

      <div class="g-button">
        <input id="test" type="button" value="Chặn bạn bè" onclick="blockFriend('minh', 5)" />
      </div>

      <div class="g-button">
        <input id="test" type="button" value="Bỏ Chặn bạn bè" onclick="unBlockFriend('minh', 5)" />
      </div>

      <hr />
      <div class="g-button">
        <input id="test" type="button" value="Get Random Users để kết bạn(minh5)" onclick="getRandomUsers('minh5', 3)" />
      </div>

      <div class="g-button">
        <input id="test" type="button" value="Get Friends Online(minh5) type = 10 (tất cả không chia page)" onclick="getFriends('minh5', 10)" />
      </div>

      <div class="g-button">
        <input id="test" type="button" value="Get Danh sách Lời mới kết bạn với mình (minh5) type = 1" onclick="getFriends('minh5', 1)" />
      </div>

      <div class="g-button">
        <input id="test" type="button" value="Danh sách bạn bè của tôi (minh5) type = 2" onclick="getFriends('minh5', 2)" />
      </div>

      <div class="g-button">
        <input id="test" type="button" value="Danh sách theo dõi (minh5) type = 3" onclick="getFriends('minh5', 3)" />
      </div>

      <div class="g-button">
        <input id="test" type="button" value="Danh sách đen (minh5) type = 4" onclick="getFriends('minh5', 4)" />
      </div>

      <div class="g-button">
        <input id="test" type="button" value="Tìm kiếm bạn bè type = 1, keyword = dvminh" onclick="searchFriends('minh5', 'dvminh', 1)" />
      </div>

      <div class="g-button">
        <input id="test" type="button" value="Tìm kiếm bạn bè type = 2, keyword = dvminh" onclick="searchFriends('minh5', 'dvminh', 2)" />
      </div>

      <div class="g-button">
        <input id="test" type="button" value="Tìm kiếm bạn bè type = 3, keyword = pnminh.it" onclick="searchFriends('minh5', 'pnminh.it', 3)" />
      </div>

      <div class="g-button">
        <input id="test" type="button" value="Tìm kiếm bạn bè type = 4, keyword = dviikkwr" onclick="searchFriends('minh5', 'dviikkwr', 4)" />
      </div>

      <div class="g-button">
        <input id="test" type="button" value="Tìm kiếm bạn bè type = 0 (all)" onclick="searchFriends('minh5', 'pnminh.it', 0)" />
      </div>

      <hr />
      <div class="g-button">
        <input id="test" type="button" value="Người mời chơi" onclick="actionSenderPlay()" />
      </div>
      <div class="g-button">
        <input id="test" type="button" value="Người được mời chơi (listen event: onInvitePlay)" onclick="actionReceiverPlay()" />
      </div>

      <hr />
      <div class="g-button">
        <input id="test" type="button" value="Danh sách nhiệm vụ" onclick="getQuests('minh5')" />
      </div>

      <div class="g-button">
        <input id="test" type="button" value="Nhận thưởng nhiệm vụ" onclick="claimQuestReward('minh5')" />
      </div>

      <hr />
      <div class="g-button">
        <input id="test" type="button" value="Chơi ngay" onclick="quickJoinTable('minh5')" />
      </div>

      <hr />
      <div class="g-button">
        <input id="test" type="button" value="Nhận thưởng đăng nhập hàng ngày" onclick="quickJoinTable('minh5')" />
      </div>

      <div class="g-button">
        <input id="test" type="button" value="Danh sách tin nhắn và email (minh4)" onclick="getMessagesAndEmail('minh4')" />
      </div>

      <hr />

      <div class="g-button">
        <input id="test" type="button" value="Quay thưởng" onclick="spinMiniGame('minh5')" />
      </div>

      
      <div class="g-button">
        <input id="test" type="button" value="Lấy thông tin luật chơi và cấu hình mini game" onclick="getMiniGameInfo('minh5')" />
      </div>

      
      <div class="g-button">
        <input id="test" type="button" value="Lấy thông tin bài may mắn ngày hôm nay" onclick="miniGameGetLuckyCard('minh5')" />
      </div>

      <div class="g-button">
        <input id="test" type="button" value="Lấy lịch sử quay thưởng" onclick="getSpinHistory('minh5')" />
      </div>

      <!-- <div class="g-button">
        <input id="test" type="button" value="Bật/tắt chế độ tự động quay" onclick="toggleAutoSpin('minh5')" />
      </div> -->

      <hr />

      <div class="g-button">
        <input id="test" type="button" value="Gửi notification đến 1 table" onclick="sendNotificationToTable('133164d0-1570-11f0-9ce3-d3e7e350b7bb')" />
      </div>
      <div class="g-button">
        <input id="test" type="button" value="Gửi notification đến tất cả bàn trong zone TS" onclick="sendNotificationToZone('TS')" />
      </div>
      <div class="g-button">
        <input id="test" type="button" value="Gửi notification đến tất cả người chơi trong game" onclick="sendNotificationToAllPlayers()" />
      </div>
      <div class="g-button">
        <input id="test" type="button" value="Gửi notification đến tất cả người chơi ở lobby" onclick="sendNotificationToLobby()" />
      </div>

      <div class="g-button">
        <input id="test" type="button" value="Gửi notification đến tất cả Zones" onclick="sendAllNotificationToZone()" />
      </div>
        
        <script>
          function sendNotificationToTable(tableId) {
            console.log("Sending notification to table: ", tableId);
            pomelo.init({
              host: host,
              port: port,
              log: true
            }, function() {
              pomelo.request("gate.gateHandler.queryEntry", {uid: 1}, function(data) {
                const _host = data?.host ?? '';
                const _port = data?.port ?? '';
                pomelo.disconnect();
                
                pomelo.init({
                  host: _host,
                  port: _port,
                  log: true
                }, function() {
                  // Login first
                  pomelo.request("connector.entryHandler.loginDevice", {deviceId: 'minh'}, function(data) {
                    console.log('Login data:', data);
                    
                    // Send notification to all tables in zone TS
                    const notificationData = {
                      tid: tableId,
                      title: "Table Announcement to table",
                      content: "Important message for all players at table",
                      type: "info"
                    };
                    
                    pomelo.request('game.notificationHandler.sendTableNotification', notificationData, function(response) {
                      console.log('Notification response:', response);
                      if (response.code === 200) {
                        alert('Notification sent successfully to table: ' + tableId);
                      } else {
                        alert('Failed to send notification: ' + response.msg);
                      }
                    });
                  });
                });
              });
            });
          } // end sendNotificationToZone

          function sendNotificationToZone(zoneName) {
            console.log("Sending notification to zone: ", zoneName);
            pomelo.init({
              host: host,
              port: port,
              log: true
            }, function() {
              pomelo.request("gate.gateHandler.queryEntry", {uid: 1}, function(data) {
                const _host = data?.host ?? '';
                const _port = data?.port ?? '';
                pomelo.disconnect();
                
                pomelo.init({
                  host: _host,
                  port: _port,
                  log: true
                }, function() {
                  // Login first
                  pomelo.request("connector.entryHandler.loginDevice", {deviceId: 'minh'}, function(data) {
                    console.log('Login data:', data);
                    
                    // Send notification to all tables in zone TS
                    const notificationData = {
                      zone: zoneName,
                      title: "Table Announcement",
                      content: "Important message for all players at TS table",
                      type: "info"
                    };
                    
                    pomelo.request('game.notificationHandler.sendZoneNotification', notificationData, function(response) {
                      console.log('Notification response:', response);
                      if (response.code === 200) {
                        alert('Notification sent successfully to all tables in zone TS');
                      } else {
                        alert('Failed to send notification: ' + response.msg);
                      }
                    });
                  });
                });
              });
            });
          } // end sendNotificationToZone

          function sendAllNotificationToZone() {
            console.log("sendAllNotificationToZone");
            pomelo.init({
              host: host,
              port: port,
              log: true
            }, function() {
              pomelo.request("gate.gateHandler.queryEntry", {uid: 1}, function(data) {
                const _host = data?.host ?? '';
                const _port = data?.port ?? '';
                pomelo.disconnect();
                
                pomelo.init({
                  host: _host,
                  port: _port,
                  log: true
                }, function() {
                  // Login first
                  pomelo.request("connector.entryHandler.loginDevice", {deviceId: 'minh'}, function(data) {
                    console.log('Login data:', data);
                    
                    // Send notification to all tables in zone TS
                    const notificationData = {
                      title: "Table Announcement",
                      content: "Important message for all players at TS table",
                      type: "info"
                    };
                    
                    pomelo.request('game.notificationHandler.sendAllZoneNotification', notificationData, function(response) {
                      console.log('Notification response:', response);
                      if (response.code === 200) {
                        alert('Notification sent successfully to all zones');
                      } else {
                        alert('Failed to send notification: ' + response.msg);
                      }
                    });
                  });
                });
              });
            });
          } // end sendAllNotificationToZone

          function sendNotificationToAllPlayers() {
            console.log("Sending notification to all playerss");
            pomelo.init({
              host: host,
              port: port,
              log: true
            }, function() {
              pomelo.request("gate.gateHandler.queryEntry", {uid: 1}, function(data) {
                const _host = data?.host ?? '';
                const _port = data?.port ?? '';
                pomelo.disconnect();
                
                pomelo.init({
                  host: _host,
                  port: _port,
                  log: true
                }, function() {
                  // Login first
                  pomelo.request("connector.entryHandler.loginDevice", {deviceId: 'minh'}, function(data) {
                    console.log('Login data:', data);
                    
                    // Send notification to all tables in zone TS
                    const notificationData = {
                      title: "Table Announcement",
                      content: "Important message for all players at tables",
                      type: "info"
                    };
                    
                    pomelo.request('game.notificationHandler.sendGlobalNotification', notificationData, function(response) {
                      console.log('Notification response:', response);
                      if (response.code === 200) {
                        alert('Notification sent successfully to all players');
                      } else {
                        alert('Failed to send notification: ' + response.msg);
                      }
                    });
                  });
                });
              });
            });
          } // end sendNotificationToAllPlayers

          function sendNotificationToLobby() {
            console.log("Sending notification to lobby");
            pomelo.init({
              host: host,
              port: port,
              log: true
            }, function() {
              pomelo.request("gate.gateHandler.queryEntry", {uid: 1}, function(data) {
                const _host = data?.host ?? '';
                const _port = data?.port ?? '';
                pomelo.disconnect();
                
                pomelo.init({
                  host: _host,
                  port: _port,
                  log: true
                }, function() {
                  // Login first
                  pomelo.request("connector.entryHandler.loginDevice", {deviceId: 'minh'}, function(data) {
                    console.log('Login data:', data);
                    
                    // Send notification to all tables in zone TS
                    const notificationData = {
                      title: "Lobby Announcement",
                      content: "Important message for all players at Lobby",
                      type: "info"
                    };
                    
                    pomelo.request('game.notificationHandler.sendLobbyNotification', notificationData, function(response) {
                      console.log('Notification response:', response);
                      if (response.code === 200) {
                        alert('Notification sent successfully to lobby');
                      } else {
                        alert('Failed to send notification: ' + response.msg);
                      }
                    });
                  });
                });
              });
            });
          } // end sendNotificationToLobby
          
        </script>

        <hr />
        <h2>Thành tích</h2>
        <div class="g-button">
          <input id="test" type="button" value="All Danh sách thành tích (minh5)" onclick="getAllBadges('minh5')" />
        </div>

        <div class="g-button">
          <input id="test" type="button" value="Nhận thưởng thành tích (minh5)" onclick="claimBadgeReward('minh5', 6)" />
        </div>

        <div class="g-button">
          <input id="test" type="button" value="Nhận thưởng thành tích CHIPS (minh5)" onclick="claimBadgeReward('minh5', 24)" />
        </div>

        <div class="g-button">
          <input id="test" type="button" value="Nhận thưởng thành tích VIP (minh5)" onclick="claimBadgeReward('minh5', 17)" />
        </div>

        <hr />
        <h2>Shop</h2>
        <div class="g-button">
          <input id="test" type="button" value="Danh sách shop items (minh5)" onclick="getShopItems('minh5')" />
        </div>
        <div class="g-button">
          <input id="test" type="button" value="Chi tiết 1 shop item (minh5)" onclick="getShopItemDetail('minh5', 1)" />
        </div>
        <div class="g-button">
          <input id="test" type="button" value="Mua 1 vật phẩm (minh5)" onclick="buyItem('minh5', 1)" />
        </div>
        <div class="g-button">
          <input id="test" type="button" value="Lịch sử mua hàng (minh5)" onclick="getPurchaseHistory('minh5')" />
        </div>

        <hr />
        <h2>Nhiệm vụ</h2>
        <div class="g-button">
          <input id="test" type="button" value="Danh sách nhiệm vụ (minh5)" onclick="getMissions('minh5')" />
        </div>
        <div class="g-button">
          <input id="test" type="button" value="Nhận thưởng nhiệm vụ (minh5)" onclick="claimRewardMission('minh5', 2)" />
        </div>

        <hr />
        <h2>Bảng xếp hạng</h2>
        <div class="g-button">
          <input id="test" type="button" value="Danh sách bảng xếp hạng (minh5)" onclick="getLeaderboards('minh3')" />
        </div>

        <div class="g-button">
          <input id="test" type="button" value="Lấy thứ hạng hiện tại của người chơi (minh3)" onclick="getPlayerRanking('minh3')" />
        </div>

        <div class="g-button">
          <input id="test" type="button" value="Lấy thứ hạng hiện tại của người chơi (minh5)" onclick="getPlayerRanking('minh5')" />
        </div>

        <br />
        <br />
        <br />

    </div>
  </body>
</html>
